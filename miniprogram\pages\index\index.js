// pages/index/index.js
const app = getApp();

Page({
  data: {
    // 平台统计数据
    platformStats: {
      totalGold: '0', // 平台累计收金(千克)
    },
    // 城市统计数据
    cityStats: {
      yesterdaySell: '27.79', // 昨日卖出克数
      todaySell: '5.44', // 今日卖出克数
      records: [] // 轮播数据，将在loadCityStats中填充
    },
    // 金价数据
    goldPrice: {
      buyPrice: 0,
      sellPrice: 0,
      updateTime: '',
      internationalPrice: '',
      shanghaiPrice: '',
      changePercent: '+0.09%',
      isUp: true
    },
    // 市场行情数据（只显示黄金9999和铂金9995）
    marketData: [],
    news: [],
    // 按钮状态
    buyButtonPressed: false,
    sellButtonPressed: false
  },

  onLoad: function (options) {
    // 检查是否有邀请参数
    if (options.inviter) {
      app.globalData.inviter = options.inviter;
    }

    // 加载所有数据
    this.loadPlatformStats();
    this.loadCityStats();

    // 加载金价数据
    this.loadGoldPriceFromAPI();
  },

  onShow: function () {

    // 刷新金价数据
    this.loadGoldPriceFromAPI();
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    Promise.all([
      this.loadPlatformStats(),
      this.loadCityStats(),
      this.loadGoldPriceFromAPI()
    ]).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onShareAppMessage: function () {
    // 分享小程序
    return {
      title: '黄金回收销售，当前金价：' + this.data.goldPrice.sellPrice + '元/克',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 跳转到商品详情
  goToProductDetail: function (e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product/detail?id=' + productId
    });
  },

  // 跳转到回收页面
  goToRecycle: function () {
    wx.navigateTo({
      url: '/pages/goldRecycle/index'
    });
  },

  // 跳转到商城页面
  goToBuy: function() {
    wx.switchTab({
      url: '/pages/shop/shop'
    });
  },

  // 买黄金按钮触摸开始
  onBuyButtonTouchStart: function() {
    this.setData({
      buyButtonPressed: true
    });
  },

  // 买黄金按钮触摸结束
  onBuyButtonTouchEnd: function() {
    this.setData({
      buyButtonPressed: false
    });
    // 添加释放动画效果
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      query.select('.buy-button').boundingClientRect();
      query.exec((res) => {
        if (res[0]) {
          // 触发释放动画
          this.setData({
            buyButtonPressed: false
          });
        }
      });
    }, 50);
  },

  // 卖黄金按钮触摸开始
  onSellButtonTouchStart: function() {
    this.setData({
      sellButtonPressed: true
    });
  },

  // 卖黄金按钮触摸结束
  onSellButtonTouchEnd: function() {
    this.setData({
      sellButtonPressed: false
    });
    // 添加释放动画效果
    setTimeout(() => {
      const query = wx.createSelectorQuery();
      query.select('.sell-button').boundingClientRect();
      query.exec((res) => {
        if (res[0]) {
          // 触发释放动画
          this.setData({
            sellButtonPressed: false
          });
        }
      });
    }, 50);
  },

  // 跳转到商城页面
  goToShop: function () {
    wx.switchTab({
      url: '/pages/shop/shop'
    });
  },

  // 跳转到分类商品列表
  goToCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    wx.switchTab({
      url: '/pages/shop/shop?category=' + category
    });
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return [year, month, day].map(this.formatNumber).join('-') + ' ' +
           [hour, minute].map(this.formatNumber).join(':');
  },

  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },

  // 加载平台统计数据
  loadPlatformStats: function() {
    return new Promise((resolve) => {
      // 这里应该从服务器获取平台统计数据
      // 模拟API调用
      setTimeout(() => {
        this.setData({
          platformStats: {
            totalGold: '38,149.33'
          }
        });
        resolve();
      }, 100);
    });
  },

  // 加载城市统计数据
  loadCityStats: function() {
    return new Promise((resolve) => {
      // 这里应该从服务器获取城市统计数据
      // 模拟API调用
      setTimeout(() => {
        // 模拟轮播数据
        const records = [
          { city: '重庆市', time: '昨日*黄', amount: '27.79' },
          { city: '送宁到*', time: '', amount: '5.44' },
          { city: '北京市', time: '今日*黄', amount: '18.65' },
          { city: '上海市', time: '昨日*黄', amount: '32.41' },
          { city: '广州市', time: '今日*黄', amount: '15.92' },
          { city: '深圳市', time: '昨日*黄', amount: '21.37' },
          { city: '成都市', time: '今日*黄', amount: '12.83' },
          { city: '杭州市', time: '昨日*黄', amount: '19.56' }
        ];

        this.setData({
          cityStats: {
            yesterdaySell: '27.79',
            todaySell: '5.44',
            records: records
          }
        });
        resolve();
      }, 100);
    });
  },

  // 从API加载金价数据
  loadGoldPriceFromAPI: function() {
    return new Promise((resolve) => {
      wx.showLoading({
        title: '加载中...',
      });

      // 先获取基础金价数据
      wx.request({
        url: 'https://api.tanshuapi.com/api/gold/v1/shgold2?key=c844cf198dc2decd1e46c05abd2d04b6',
        method: 'GET',
        success: (res) => {
          if (res.statusCode === 200 && res.data && res.data.code === 1) {
            const goldData = res.data.data.list;

            // 提取黄金9999的数据作为主要金价
            const mainGold = goldData.Au9999 || {};

            // 提取铂金9995数据
            const mainPlatinum = goldData.PT9995 || {};

            // 获取基础价格后，再获取价格控制浮动值
            this.loadPriceControlsAndUpdate(mainGold, mainPlatinum, goldData, resolve);
          } else {
            console.error('获取金价数据失败', res);
            // 使用默认数据
            this.setDefaultGoldData();
            wx.hideLoading();
            resolve();
          }
        },
        fail: (err) => {
          console.error('请求金价API失败', err);
          // 使用默认数据
          this.setDefaultGoldData();
          wx.hideLoading();
          resolve();
        }
      });
    });
  },

  // 加载价格控制数据并更新价格显示
  loadPriceControlsAndUpdate: function(mainGold, mainPlatinum, goldData, resolve) {
    console.log('开始加载价格控制数据...');
    console.log('基础金价数据:', mainGold);
    console.log('基础铂金价数据:', mainPlatinum);

    // 并行获取金价和铂金价的浮动控制数据
    Promise.all([
      this.loadPriceControl(1), // 金价浮动
      this.loadPriceControl(2)  // 铂金价浮动
    ]).then((results) => {
      const [goldControl, platinumControl] = results;

      console.log('金价浮动控制数据:', goldControl);
      console.log('铂金价浮动控制数据:', platinumControl);

      // 计算最终价格（基础价格 + 浮动价格）
      const finalGoldBuyPrice = this.calculateFinalPrice(mainGold.buyprice, goldControl.recyclePrice);
      const finalGoldSellPrice = this.calculateFinalPrice(mainGold.sellprice, goldControl.sellPrice);
      const finalPlatinumBuyPrice = this.calculateFinalPrice(mainPlatinum.buyprice, platinumControl.recyclePrice);
      const finalPlatinumSellPrice = this.calculateFinalPrice(mainPlatinum.sellprice, platinumControl.sellPrice);

      console.log('计算后的最终价格:');
      console.log(`金价 - 买入: ${finalGoldBuyPrice}, 卖出: ${finalGoldSellPrice}`);
      console.log(`铂金价 - 买入: ${finalPlatinumBuyPrice}, 卖出: ${finalPlatinumSellPrice}`);

      // 更新主要金价数据
      this.setData({
        goldPrice: {
          buyPrice: finalGoldBuyPrice.toFixed(2),
          sellPrice: finalGoldSellPrice.toFixed(2),
          updateTime: mainGold.updatetime || '',
          internationalPrice: '',
          shanghaiPrice: finalGoldSellPrice.toFixed(2),
          changePercent: mainGold.changepercent || '+0.00%',
          isUp: (mainGold.changequantity && parseFloat(mainGold.changequantity) >= 0) ? true : false
        }
      });

      // 处理市场行情数据（显示固定的4个项目）
      const marketData = [];

      // 1. 黄金9999（对应API中的Au9999）- 应用金价控制器浮动值
      if (goldData.Au9999) {
        const au9999 = goldData.Au9999;
        marketData.push({
          id: 'Au9999',
          name: '黄金9999',
          buyPrice: this.calculateFinalPrice(au9999.buyprice, goldControl.recyclePrice).toFixed(2),
          sellPrice: this.calculateFinalPrice(au9999.sellprice, goldControl.sellPrice).toFixed(2),
          maxPrice: this.calculateFinalPrice(au9999.maxprice, goldControl.sellPrice).toFixed(2),
          minPrice: this.calculateFinalPrice(au9999.minprice, goldControl.recyclePrice).toFixed(2)
        });
      }

      // 2. 黄金T+D（对应API中的AuT+D）- 应用金价控制器浮动值
      if (goldData['AuT+D']) {
        const autd = goldData['AuT+D'];
        marketData.push({
          id: 'AuTD',
          name: '黄金T+D',
          buyPrice: this.calculateFinalPrice(autd.buyprice, goldControl.recyclePrice).toFixed(2),
          sellPrice: this.calculateFinalPrice(autd.sellprice, goldControl.sellPrice).toFixed(2),
          maxPrice: this.calculateFinalPrice(autd.maxprice, goldControl.sellPrice).toFixed(2),
          minPrice: this.calculateFinalPrice(autd.minprice, goldControl.recyclePrice).toFixed(2)
        });
      }

      // 3. 现货黄金（固定显示项，使用合理的默认值）- 应用金价控制器浮动值
      marketData.push({
        id: 'SpotGold',
        name: '现货黄金',
        buyPrice: this.calculateFinalPrice(mainGold.buyprice, goldControl.recyclePrice).toFixed(2),
        sellPrice: this.calculateFinalPrice(mainGold.sellprice, goldControl.sellPrice).toFixed(2),
        maxPrice: this.calculateFinalPrice(mainGold.maxprice, goldControl.sellPrice).toFixed(2),
        minPrice: this.calculateFinalPrice(mainGold.minprice, goldControl.recyclePrice).toFixed(2)
      });

      // 4. 铂金9995（对应API中的PT9995）- 应用铂金价控制器浮动值
      if (goldData.PT9995) {
        const pt9995 = goldData.PT9995;
        marketData.push({
          id: 'PT9995',
          name: '铂金9995',
          buyPrice: this.calculateFinalPrice(pt9995.buyprice, platinumControl.recyclePrice).toFixed(2),
          sellPrice: this.calculateFinalPrice(pt9995.sellprice, platinumControl.sellPrice).toFixed(2),
          maxPrice: this.calculateFinalPrice(pt9995.maxprice, platinumControl.sellPrice).toFixed(2),
          minPrice: this.calculateFinalPrice(pt9995.minprice, platinumControl.recyclePrice).toFixed(2)
        });
      }

      this.setData({
        marketData: marketData
      });

      wx.hideLoading();
      resolve();
    }).catch((error) => {
      console.error('获取价格控制数据失败:', error);
      // 如果价格控制API失败，使用原始价格
      this.fallbackToOriginalPrices(mainGold, mainPlatinum, goldData);
      wx.hideLoading();
      resolve();
    });
  },

  // 加载价格控制数据
  loadPriceControl: function(priceType) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiConfig.baseUrl}/api/price-controls/type/${priceType}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log(`价格控制API响应 (priceType=${priceType}):`, res);

          if (res.data.code === 200 && res.data.data) {
            // API调用成功，返回浮动价格数据
            resolve({
              recyclePrice: res.data.data.recyclePrice || 0,
              sellPrice: res.data.data.sellPrice || 0
            });
          } else {
            console.warn(`价格控制API返回错误 (priceType=${priceType}):`, res.data);
            // API返回错误，使用0作为浮动值（即不浮动）
            resolve({
              recyclePrice: 0,
              sellPrice: 0
            });
          }
        },
        fail: (error) => {
          console.error(`价格控制API请求失败 (priceType=${priceType}):`, error);
          // API请求失败，使用0作为浮动值（即不浮动）
          resolve({
            recyclePrice: 0,
            sellPrice: 0
          });
        }
      });
    });
  },

  // 计算最终价格（基础价格 + 浮动价格）
  calculateFinalPrice: function(basePrice, floatPrice) {
    const base = parseFloat(basePrice) || 0;
    const float = parseFloat(floatPrice) || 0;
    const result = base + float;

    // 确保结果为正数，避免显示负价格
    return Math.max(result, 0);
  },

  // 当价格控制API失败时，回退到原始价格
  fallbackToOriginalPrices: function(mainGold, mainPlatinum, goldData) {
    // 更新金价数据（使用原始价格）
    this.setData({
      goldPrice: {
        buyPrice: mainGold.buyprice || '0',
        sellPrice: mainGold.sellprice || '0',
        updateTime: mainGold.updatetime || '',
        internationalPrice: '',
        shanghaiPrice: mainGold.price || '0',
        changePercent: mainGold.changepercent || '+0.00%',
        isUp: (mainGold.changequantity && parseFloat(mainGold.changequantity) >= 0) ? true : false
      }
    });

    // 处理市场行情数据（显示固定的4个项目）
    const marketData = [];

    // 1. 黄金9999（对应API中的Au9999）
    if (goldData.Au9999) {
      const au9999 = goldData.Au9999;
      marketData.push({
        id: 'Au9999',
        name: '黄金9999',
        buyPrice: au9999.buyprice || '-',
        sellPrice: au9999.sellprice || '-',
        maxPrice: au9999.maxprice || '-',
        minPrice: au9999.minprice || '-'
      });
    }

    // 2. 黄金T+D（对应API中的AuT+D）
    if (goldData['AuT+D']) {
      const autd = goldData['AuT+D'];
      marketData.push({
        id: 'AuTD',
        name: '黄金T+D',
        buyPrice: autd.buyprice || '-',
        sellPrice: autd.sellprice || '-',
        maxPrice: autd.maxprice || '-',
        minPrice: autd.minprice || '-'
      });
    }

    // 3. 现货黄金（固定显示项，使用合理的默认值）
    marketData.push({
      id: 'SpotGold',
      name: '现货黄金',
      buyPrice: mainGold.buyprice || '-',
      sellPrice: mainGold.sellprice || '-',
      maxPrice: mainGold.maxprice || '-',
      minPrice: mainGold.minprice || '-'
    });

    // 4. 铂金9995（对应API中的PT9995）
    if (goldData.PT9995) {
      const pt9995 = goldData.PT9995;
      marketData.push({
        id: 'PT9995',
        name: '铂金9995',
        buyPrice: pt9995.buyprice || '-',
        sellPrice: pt9995.sellprice || '-',
        maxPrice: pt9995.maxprice || '-',
        minPrice: pt9995.minprice || '-'
      });
    }

    this.setData({
      marketData: marketData
    });
  },

  // 设置默认金价数据（当API请求失败时使用）
  setDefaultGoldData: function() {
    this.setData({
      goldPrice: {
        buyPrice: '552.30',
        sellPrice: '552.50',
        updateTime: '2024-05-28 17:22:01',
        internationalPrice: '',
        shanghaiPrice: '552.50',
        changePercent: '+0.16%',
        isUp: true
      },
      marketData: [
        {
          id: 'Au9999',
          name: '黄金9999',
          buyPrice: '552.30',
          sellPrice: '552.50',
          maxPrice: '556.30',
          minPrice: '552.50'
        },
        {
          id: 'AuTD',
          name: '黄金T+D',
          buyPrice: '550.80',
          sellPrice: '551.00',
          maxPrice: '555.00',
          minPrice: '550.00'
        },
        {
          id: 'SpotGold',
          name: '现货黄金',
          buyPrice: '552.00',
          sellPrice: '552.20',
          maxPrice: '556.00',
          minPrice: '552.00'
        },
        {
          id: 'PT9995',
          name: '铂金9995',
          buyPrice: '210.50',
          sellPrice: '211.00',
          maxPrice: '215.00',
          minPrice: '210.00'
        }
      ]
    });
  },

  // 跳转到个人中心
  goToProfile: function() {
    wx.switchTab({
      url: '/pages/my/my'
    });
  },

  // 联系客服
  contactService: function() {
    // 跳转到客服页面，传递特定的订单号
    wx.navigateTo({
      url: '/pages/customerService/index?orderId=首页咨询',
      fail: (error) => {
        console.error('跳转客服页面失败:', error);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });
  }
})
