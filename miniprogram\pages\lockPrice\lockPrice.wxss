/* pages/lockPrice/lockPrice.wxss */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  padding: 20rpx;
  padding-bottom: 30rpx;
}

/* 用户信息区域 */
.user-info-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background-color: #222;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.user-name {
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
}

.user-id {
  font-size: 26rpx;
  color: #ccc;
}

.login-status {
  display: flex;
  align-items: center;
  background-color: #333;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

.status-icon {
  color: #4CAF50;
  font-size: 28rpx;
  margin-right: 6rpx;
}

.status-text {
  color: #fff;
  font-size: 24rpx;
}

/* 可用定金卡片 */
.balance-card {
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  border-radius: 16rpx;
  padding: 30rpx;
  color: #fff;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 56, 13, 0.2);
}

.balance-title {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.balance-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
  padding-top: 20rpx;
}

.detail-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.detail-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 6rpx;
}

.detail-value {
  font-size: 32rpx;
  font-weight: bold;
}

.divider {
  width: 1rpx;
  height: 50rpx;
  background-color: rgba(255, 255, 255, 0.2);
}

/* 黄金价格信息 */
.gold-info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.gold-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.gold-icon {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.gold-inventory {
  font-size: 24rpx;
  color: #666;
}

.gold-price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gold-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF4D4F;
}

/* 锁价按钮区域 */
.lock-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.lock-button {
  width: 48%;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.lock-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
  font-size: 28rpx;
}

.lock-text {
  font-size: 30rpx;
  font-weight: bold;
}

.buy-button {
  background-color: #9254DE;
  color: #fff;
}

.sell-button {
  background-color: #5B8FF9;
  color: #fff;
}

/* 郑重提醒 */
.reminder-section {
  background-color: #FFF9E6;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.reminder-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #D48806;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.reminder-content {
  font-size: 26rpx;
  color: #D48806;
}

.reminder-item {
  margin-bottom: 10rpx;
  line-height: 1.5;
}