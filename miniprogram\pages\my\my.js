// pages/my/my.js
const app = getApp();

Page({
  data: {
    userInfo: null,
    isLogin: false, // 是否已登录
    goldStorage: 0, // 黄金存料克数
    orderCounts: {
      sell: 0,
      buy: 0,
      jewelry: 0
    },
    walletBalance: '0.00', // 钱包余额
    walletPoints: '0', // 积分
    accountInfo: {
      balance: '0.00',
      totalRebate: '0.00'
    }
  },

  onLoad: function (options) {
    // 检查本地存储中是否有用户信息
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo && userInfo.isLogin) {
      app.globalData.userInfo = userInfo;
      this.setData({
        userInfo: userInfo,
        isLogin: true
      });
      // 获取账户信息
      this.getAccountInfo();
    }
  },

  onShow: function () {
    // 检查用户登录状态
    if (app.globalData.userInfo && app.globalData.userInfo.isLogin) {
      this.setData({
        userInfo: app.globalData.userInfo,
        isLogin: true
      });

      // 获取账户信息
      this.getAccountInfo();
    } else {
      // 未登录状态
      this.setData({
        isLogin: false,
        userInfo: null,
        walletBalance: '0.00',
        walletPoints: '0',
        accountInfo: {
          balance: '0.00',
          totalRebate: '0.00'
        }
      });
    }
  },

  onShareAppMessage: function () {
    return {
      title: '黄金回收销售，邀请好友享受佣金',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 获取用户信息
  getUserProfile: function () {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        // 保存用户信息到云数据库
        this.saveUserInfo(res.userInfo);
      }
    });
  },

  // 保存用户信息
  saveUserInfo: function (userInfo) {
    // 创建用户信息对象
    const newUser = {
      id: new Date().getTime().toString(),
      nickname: userInfo.nickName,
      avatar: userInfo.avatarUrl,
      phone: '',
      address: {},
      inviter: app.globalData.inviter || '',
      inviteCount: 0,
      commission: 0,
      createTime: new Date(),
      isLogin: true
    };

    // 更新全局用户信息
    app.globalData.userInfo = newUser;

    // 保存到本地存储
    wx.setStorageSync('userInfo', newUser);

    // 更新页面状态
    this.setData({
      userInfo: newUser,
      isLogin: true
    });

    wx.showToast({
      title: '登录成功',
      icon: 'success'
    });
  },

  // 跳转到设置页面
  goToSettings: function () {
    wx.navigateTo({
      url: '/pages/settings/index'
    });
  },

  // 跳转到订单列表
  goToOrderList: function (e) {
    const type = e.currentTarget.dataset.type || '';
    wx.navigateTo({
      url: '/pages/order/list?type=' + type
    });
  },

  // 跳转到提货记录
  goToDeliveryRecord: function () {
    wx.navigateTo({
      url: '/pages/goldStorage/deliveryRecord'
    });
  },

  // 跳转到地址管理
  goToAddressManage: function () {
    wx.navigateTo({
      url: '/pages/address/index'
    });
  },

  // 跳转到客户管理
  goToCustomerManage: function () {
    const userLevel = this.data.userInfo?.userLevel;
    if (userLevel === 0 || userLevel === 1) {
      wx.showToast({
        title: '仅受邀用户可参加活动',
        icon: 'none',
        duration: 2000
      });
      return;
    }
    wx.navigateTo({
      url: '/pages/customer/index'
    });
  },

  // 跳转到邀请有礼
  goToInvite: function () {
    wx.navigateTo({
      url: '/pages/invite/index'
    });
  },

  // 跳转到商品详情
  goToProductDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product/detail?id=' + id
    });
  },

  // 跳转到钱包页面
  goToWallet: function() {
    wx.navigateTo({
      url: '/pages/wallet/index'
    });
  },

  // 跳转到登录页面
  goToLogin: function() {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  // 跳转到实名认证页面
  goToVerification: function() {
    wx.navigateTo({
      url: '/pages/verification/index'
    });
  },

  // 退出登录
  logout: function() {
    // 清除全局用户信息
    app.globalData.userInfo = null;

    // 清除本地存储的用户信息
    wx.removeStorageSync('userInfo');

    // 更新页面状态
    this.setData({
      isLogin: false,
      userInfo: null,
      walletBalance: '0.00',
      walletPoints: '0',
      accountInfo: {
        balance: '0.00',
        totalRebate: '0.00'
      }
    });

    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
  },

  // 获取账户信息
  getAccountInfo() {
    if (!this.data.isLogin || !this.data.userInfo) return;
    
    const accountId = this.data.userInfo.account;
    if (!accountId) {
      console.log('accountId is empty');
      return;
    }

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/accounts/account/${accountId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('Account info response:', res);
        if (res.data.code === 200) {
          const accountData = res.data.data;
          this.setData({
            accountInfo: {
              balance: accountData.balance.toFixed(2),
              totalRebate: accountData.totalRebate.toFixed(2)
            }
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取账户信息失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('Request failed:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  }
})
