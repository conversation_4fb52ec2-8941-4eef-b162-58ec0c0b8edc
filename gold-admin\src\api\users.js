import request from '@/utils/request'

// 获取用户列表
export function getUserList(params) {
  return request({
    url: '/api/admin/users',
    method: 'get',
    params
  })
}

// 获取用户详情
export function getUserDetail(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'get'
  })
}

// 创建用户
export function createUser(data) {
  return request({
    url: '/admin/users',
    method: 'post',
    data
  })
}

// 更新用户
export function updateUser(id, data) {
  return request({
    url: `/admin/users/${id}`,
    method: 'put',
    data
  })
}

// 删除用户
export function deleteUser(id) {
  return request({
    url: `/admin/users/${id}`,
    method: 'delete'
  })
}

// 禁用/启用用户
export function toggleUserStatus(id, status) {
  return request({
    url: `/admin/users/${id}/status`,
    method: 'patch',
    data: { status }
  })
}

// 重置用户密码
export function resetUserPassword(id) {
  return request({
    url: `/admin/users/${id}/reset-password`,
    method: 'post'
  })
}

// 修改用户等级
export function updateUserLevel(account, userLevel) {
  return request({
    url: `/api/admin/users/${account}`,
    method: 'put',
    params: { userLevel }
  })
}

// 获取用户关系树
export function getUserRelationTree(account) {
  return request({
    url: `/api/admin/users/${account}/relation-tree`,
    method: 'get'
  })
}

// 获取用户关系
export function getUserRelations(account) {
  return request({
    url: `/api/user-relation/${account}`,
    method: 'get'
  })
}

// 获取用户直接下级
export function getUserChildren(account) {
  return request({
    url: `/api/user-relation/${account}/children`,
    method: 'get'
  })
}

// 删除用户关系
export function deleteUserRelation(relationId) {
  return request({
    url: `/api/user-relation/${relationId}`,
    method: 'delete'
  })
}

// 获取用户密码
export function getUserPassword(account) {
  return request({
    url: `/api/user/password/${account}`,
    method: 'get'
  })
}

// 修改用户密码
export function updateUserPassword(account, password) {
  return request({
    url: `/api/user/account/${account}`,
    method: 'put',
    params: { password }
  })
}
