// pages/login/login.js
const app = getApp();
const userApi = require('../../utils/userapi');

Page({
  data: {
    account: '',
    password: ''
  },

  // 输入账号
  inputAccount: function(e) {
    this.setData({
      account: e.detail.value
    });
  },

  // 输入密码
  inputPassword: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 登录
  login: function() {
    const { account, password } = this.data;

    // 表单验证
    if (!account) {
      wx.showToast({
        title: '请输入账号',
        icon: 'none'
      });
      return;
    }

    if (!password) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    // 显示加载中
    wx.showLoading({
      title: '登录中...',
    });

    // 使用API模块发送登录请求
    userApi.login(account, password)
      .then(res => {
        wx.hideLoading();
        console.log(res.data)
        if (res.code === 200) {
          // 登录成功
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });

          // 从API返回的data.data中获取token和用户信息
          const apiData = res.data; // 获取data对象
          console.log(apiData)
          if (apiData && apiData.token) {
             wx.setStorageSync('token', apiData.token); // 保存token到本地存储
             // 保存用户信息 (apiData 包含了所有用户信息字段)
             userApi.saveUserInfo(apiData); 
          } else {
             console.error('API返回数据结构异常：缺少token或data字段', res);
             wx.showToast({
               title: '登录失败：数据异常',
               icon: 'none'
             });
             return; // 数据异常，不进行后续操作
          }

          // 延迟跳转到我的页面
          setTimeout(() => {
            wx.switchTab({
              url: '/pages/my/my'
            });
          }, 1500);
        } else {
          // 登录失败
          wx.showToast({
            title: res.msg || '登录失败，请检查账号密码',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: err.msg || '网络错误，请重试',
          icon: 'none'
        });
        console.error('登录请求失败', err);
      });
  },

  // 显示用户协议
  showUserAgreement: function() {
    wx.navigateTo({
      url: '/pages/service/index'
    });
  },

  // 显示隐私政策
  showPrivacyPolicy: function() {
    wx.navigateTo({
      url: '/pages/privacy/index'
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 跳转到注册页
  goToRegister: function() {
    wx.navigateTo({
      url: '/pages/login/register'
    });
  }
})
