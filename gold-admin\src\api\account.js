import request from '@/utils/request'

/**
 * 获取账户交易审核列表
 * @param {object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 * @param {number} params.reviewStatus - 审核状态 (0=待审核, 1=已通过, 2=已拒绝)
 * @param {number} params.transactionType - 交易类型 (1=充值, 2=提现)
 */
export function getAccountReviewList(params) {
  return request({
    url: '/api/account-review/list',
    method: 'get',
    params
  })
}

/**
 * 审核账户交易
 * @param {object} data - 审核数据
 * @param {string} data.accountId - 账户ID
 * @param {string} data.transactionId - 交易ID
 * @param {number} data.reviewStatus - 审核状态 (1=通过, 2=拒绝)
 */
export function reviewAccountTransaction(data) {
  return request({
    url: '/api/account-review/review',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data
  })
}
