import request from '@/utils/request'

/**
 * 获取锁价订单列表
 * @param {object} params - 查询参数
 */
export function getLockOrderList(params) {
  return request({
    url: '/admin/lock-orders/list',
    method: 'get',
    params
  })
}

/**
 * 获取锁价订单详情
 * @param {number} id - 订单ID
 */
export function getLockOrderDetail(id) {
  return request({
    url: `/admin/lock-orders/${id}`,
    method: 'get'
  })
}

/**
 * 更新锁价订单状态
 * @param {number} id - 订单ID
 * @param {object} data - 更新数据
 */
export function updateLockOrderStatus(id, data) {
  return request({
    url: `/admin/lock-orders/${id}/status`,
    method: 'put',
    data
  })
}

/**
 * 导出锁价订单
 * @param {object} params - 查询参数
 */
export function exportLockOrders(params) {
  return request({
    url: '/admin/lock-orders/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取锁价订单统计
 */
export function getLockOrderStats() {
  return request({
    url: '/admin/lock-orders/stats',
    method: 'get'
  })
}
