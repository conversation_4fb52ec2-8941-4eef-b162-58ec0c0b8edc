<template>
  <div class="app-container">
    <div class="account-header">
      <h2>充值/提现管理</h2>
      <p>管理用户充值和提现申请的审核</p>
    </div>

    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-select
        v-model="listQuery.reviewStatus"
        placeholder="审核状态"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="待审核" :value="0" />
        <el-option label="已通过" :value="1" />
        <el-option label="已拒绝" :value="2" />
      </el-select>
      
      <el-select
        v-model="listQuery.transactionType"
        placeholder="交易类型"
        clearable
        style="width: 120px"
        class="filter-item"
      >
        <el-option label="充值" :value="1" />
        <el-option label="提现" :value="2" />
      </el-select>
      
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
      
      <el-button
        class="filter-item"
        icon="Refresh"
        @click="handleReset"
      >
        重置
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      style="width: 100%"
    >
      <el-table-column prop="transactionId" label="交易ID" width="180" />
      <el-table-column prop="accountId" label="账户ID" width="120" />
      <el-table-column label="转账凭证" width="120" align="center">
        <template #default="scope">
          <div v-if="scope.row.transferImageUrl" class="transfer-image-container">
            <el-image
              :src="scope.row.transferImageUrl"
              :preview-src-list="[scope.row.transferImageUrl]"
              fit="cover"
              class="transfer-image-preview"
              :preview-teleported="true"
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
            </el-image>
          </div>
          <span v-else class="text-muted">无凭证</span>
        </template>
      </el-table-column>
      <el-table-column label="交易类型" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getTransactionTypeColor(scope.row.transactionType)">
            {{ getTransactionTypeText(scope.row.transactionType) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金额" width="120" align="right">
        <template #default="scope">
          ¥{{ formatAmount(scope.row.amount) }}
        </template>
      </el-table-column>
      <el-table-column label="审核状态" width="100" align="center">
        <template #default="scope">
          <el-tag :type="getReviewStatusColor(scope.row.reviewStatus)">
            {{ getReviewStatusText(scope.row.reviewStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="申请时间" width="160">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="reviewTime" label="审核时间" width="160">
        <template #default="scope">
          {{ scope.row.reviewTime ? formatTime(scope.row.reviewTime) : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template #default="scope">
          <el-button
            v-if="scope.row.reviewStatus === 0"
            type="success"
            size="small"
            @click="handleReview(scope.row, 1)"
          >
            通过
          </el-button>
          <el-button
            v-if="scope.row.reviewStatus === 0"
            type="danger"
            size="small"
            @click="handleReview(scope.row, 2)"
          >
            拒绝
          </el-button>
          <span v-else class="text-muted">已审核</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      @pagination="getList"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Picture } from '@element-plus/icons-vue'
import { getAccountReviewList, reviewAccountTransaction } from '@/api/account'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(false)
const listQuery = ref({
  page: 1,
  size: 10,
  reviewStatus: null,
  transactionType: null
})

async function getList() {
  listLoading.value = true
  try {
    const response = await getAccountReviewList(listQuery.value)
    list.value = response.data?.records || []
    total.value = response.data?.total || 0
    console.log('获取账户交易审核列表成功:', response.data)
  } catch (error) {
    console.error('获取账户交易审核列表失败:', error)
    ElMessage.error(`获取列表失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
    
    // 开发环境下显示模拟数据
    if (process.env.NODE_ENV === 'development') {
      list.value = [
        {
          transactionId: 'TXN20231201001',
          accountId: 'ACC001',
          username: '张三',
          transactionType: 1, // 充值
          amount: 1000.00,
          reviewStatus: 0, // 待审核
          createTime: '2025-06-04T10:30:00',
          reviewTime: null,
          transferImageUrl: 'https://via.placeholder.com/300x200/409EFF/ffffff?text=Transfer+Receipt+1'
        },
        {
          transactionId: 'TXN20231201002',
          accountId: 'ACC002',
          username: '李四',
          transactionType: 2, // 提现
          amount: 500.00,
          reviewStatus: 1, // 已通过
          createTime: '2025-06-03T15:20:00',
          reviewTime: '2025-06-03T16:00:00',
          transferImageUrl: 'https://via.placeholder.com/300x200/67C23A/ffffff?text=Transfer+Receipt+2'
        },
        {
          transactionId: 'TXN20231201003',
          accountId: 'ACC003',
          username: '王五',
          transactionType: 1, // 充值
          amount: 2000.00,
          reviewStatus: 2, // 已拒绝
          createTime: '2025-06-02T09:15:00',
          reviewTime: '2025-06-02T10:30:00',
          transferImageUrl: null // 无凭证示例
        }
      ]
      total.value = 3
    }
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

function handleReset() {
  listQuery.value = {
    page: 1,
    size: 20,
    reviewStatus: null,
    transactionType: null
  }
  getList()
}

async function handleReview(row, reviewStatus) {
  const actionText = reviewStatus === 1 ? '通过' : '拒绝'
  const confirmText = `确定要${actionText}该${getTransactionTypeText(row.transactionType)}申请吗？`
  
  try {
    await ElMessageBox.confirm(confirmText, '确认审核', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await reviewAccountTransaction({
      accountId: row.accountId,
      transactionId: row.transactionId,
      reviewStatus
    })
    
    ElMessage.success(`审核${actionText}成功`)
    getList() // 刷新列表
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审核失败:', error)
      ElMessage.error(`审核失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
    }
  }
}

function getTransactionTypeColor(type) {
  const colorMap = {
    1: 'success', // 充值
    2: 'warning'  // 提现
  }
  return colorMap[type] || 'info'
}

function getTransactionTypeText(type) {
  const textMap = {
    1: '充值',
    2: '提现'
  }
  return textMap[type] || '未知'
}

function getReviewStatusColor(status) {
  const colorMap = {
    0: 'warning', // 待审核
    1: 'success', // 已通过
    2: 'danger'   // 已拒绝
  }
  return colorMap[status] || 'info'
}

function getReviewStatusText(status) {
  const textMap = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝'
  }
  return textMap[status] || '未知'
}

function formatAmount(amount) {
  if (amount === null || amount === undefined) return '0.00'
  return Number(amount).toFixed(2)
}

function formatTime(time) {
  if (!time) return '-'
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.account-header {
  margin-bottom: 20px;
}

.account-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.account-header p {
  margin: 0;
  color: #909399;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.text-muted {
  color: #909399;
}

.transfer-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.transfer-image-preview {
  width: 60px;
  height: 60px;
  border-radius: 4px;
  cursor: pointer;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background-color: #f5f7fa;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  color: #909399;
  font-size: 12px;
}

.image-error .el-icon {
  font-size: 20px;
  margin-bottom: 4px;
}
</style>
