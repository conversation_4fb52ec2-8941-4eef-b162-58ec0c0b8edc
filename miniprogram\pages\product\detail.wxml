<!--pages/product/detail.wxml-->
<view class="container">
  <!-- 商品轮播图 -->
  <swiper class="product-swiper" indicator-dots="{{true}}" autoplay="{{true}}" interval="3000" duration="500" circular="{{true}}" bindchange="swiperChange">
    <block wx:for="{{product.images}}" wx:key="*this">
      <swiper-item>
        <image src="{{item}}" class="product-image" mode="aspectFill" bindtap="previewImage" data-src="{{item}}"></image>
      </swiper-item>
    </block>
  </swiper>
  <view class="swiper-indicator">{{currentSwiper + 1}}/{{product.images.length}}</view>

  <!-- 商品信息 -->
  <view class="product-info-card">
    <view class="product-price-row">
      <view class="product-price">¥{{product.price/100}}</view>
      <view class="product-tags">
        <view class="product-tag hot" wx:if="{{product.isHot}}">热门</view>
        <view class="product-tag new" wx:if="{{product.isNew}}">新品</view>
      </view>
    </view>
    <view class="product-title">{{product.name}}</view>
    <view class="product-meta">
      <view class="product-meta-item">
        <text class="meta-label">重量：</text>
        <text class="meta-value">{{product.goldWeight}}克</text>
      </view>
      <view class="product-meta-item">
        <text class="meta-label">纯度：</text>
        <text class="meta-value">{{product.purity}}</text>
      </view>
      <view class="product-meta-item">
        <text class="meta-label">销量：</text>
        <text class="meta-value">{{product.sales}}件</text>
      </view>
    </view>
    <view class="gold-price-tip">
      <text>当前金价：¥{{goldPrice.sellPrice/100}}元/克</text>
    </view>
  </view>

  <!-- 商品数量 -->
  <view class="product-quantity-card">
    <view class="quantity-title">购买数量</view>
    <view class="quantity-stepper">
      <view class="stepper-minus {{quantity <= 1 ? 'disabled' : ''}}" bindtap="minusQuantity">-</view>
      <input class="stepper-input" type="number" value="{{quantity}}" bindchange="inputQuantity" />
      <view class="stepper-plus {{quantity >= product.stock ? 'disabled' : ''}}" bindtap="addQuantity">+</view>
    </view>
  </view>

  <!-- 商品详情 -->
  <view class="product-detail-card">
    <view class="detail-title">商品详情</view>
    <view class="detail-content">
      <rich-text nodes="{{product.description}}"></rich-text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="bottom-actions">
      <view class="action-item" bindtap="toggleFavorite">
        <image class="action-icon" src="{{isFavorite ? '/images/icons/favorite_filled.png' : '/images/icons/favorite.png'}}"></image>
        <view class="action-text">收藏</view>
      </view>
      <button class="action-item share-btn" open-type="share">
        <image class="action-icon" src="/images/icons/share.png"></image>
        <view class="action-text">分享</view>
      </button>
      <view class="action-item" bindtap="showShare">
        <image class="action-icon" src="/images/icons/poster.png"></image>
        <view class="action-text">海报</view>
      </view>
    </view>
    <view class="bottom-buttons">
      <view class="buy-button" bindtap="buyNow">立即购买</view>
    </view>
  </view>

  <!-- 分享面板 -->
  <view class="share-modal {{showShareModal ? 'show' : ''}}">
    <view class="share-mask" bindtap="hideShare"></view>
    <view class="share-content">
      <view class="share-header">
        <view class="share-title">分享给好友</view>
        <view class="share-close" bindtap="hideShare">×</view>
      </view>
      <view class="share-body">
        <view class="share-image-container">
          <image class="share-image" src="{{shareImage}}" mode="aspectFit"></image>
        </view>
        <view class="share-tip">保存图片，分享给好友</view>
        <view class="share-code">邀请码: {{shareCode}}</view>
        <view class="share-commission-tip">好友通过您的邀请购买，您将获得订单金额5%的佣金</view>
        <view class="share-save-button" bindtap="saveShareImage">保存图片</view>
      </view>
    </view>
  </view>
</view>
