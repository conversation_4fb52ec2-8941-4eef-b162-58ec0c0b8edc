/* pages/verification/index.wxss */
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 顶部提示样式 */
.header-tips {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #FFF9E6 0%, #FFF0D9 100%);
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
  border: 2rpx solid #FFD700;
}

.tips-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #FFD700;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
}

.tips-text {
  font-size: 28rpx;
  color: #4A1010;
  flex: 1;
}

/* 表单区域样式 */
.form-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  background-color: #f8f8f8;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input::placeholder {
  color: #999;
}

/* 上传区域样式 */
.upload-section {
  margin-top: 40rpx;
}

.upload-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.upload-tips {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.upload-box {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.upload-item {
  flex: 1;
}

.upload-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.upload-content {
  width: 100%;
  height: 320rpx;
  background-color: #f8f8f8;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.upload-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #999;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

/* 协议同意区域样式 */
.agreement-section {
  margin-top: 40rpx;
  padding: 0 30rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.checkbox {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background-color: #4A1010;
  border-color: #4A1010;
}

.checkbox-icon {
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
}

.agreement-text {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.agreement-link {
  color: #4A1010;
  text-decoration: underline;
  font-weight: 500;
  margin-left: 8rpx;
}

/* 提交按钮样式 */
.submit-section {
  margin-top: 30rpx;
  padding: 0 30rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #4A1010 0%, #6B1A1A 100%);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(74, 16, 16, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn[loading] {
  opacity: 0.8;
}

.submit-btn.disabled {
  background: #ccc;
  color: #999;
  box-shadow: none;
}

/* 底部提示样式 */
.footer-tips {
  margin-top: 40rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.tips-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
}

.tips-content {
  padding-left: 20rpx;
}

.tips-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
  position: relative;
  padding-left: 20rpx;
}

.tips-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 16rpx;
  width: 8rpx;
  height: 8rpx;
  background-color: #4A1010;
  border-radius: 50%;
}

.tips-item:last-child {
  margin-bottom: 0;
}