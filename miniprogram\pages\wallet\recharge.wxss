/* pages/wallet/recharge.wxss */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  padding-bottom: 40rpx;
  background-color: #FFF7F7;
}

/* 客服二维码 */
.qrcode-section {
  margin: 10rpx 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qrcode-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.qrcode-subtitle {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
  text-align: center;
}

.qrcode-container {
  width: 400rpx;
  height: 400rpx;
  background-color: #f9f9f9;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.qrcode-image {
  width: 100%;
  height: 100%;
}

.qrcode-tips {
  font-size: 26rpx;
  color: #FF4D4F;
  margin-top: 10rpx;
}

/* 充值表单 */
.form-section {
  margin: 20rpx 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.form-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.form-textarea {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

/* 上传图片 */
.upload-container {
  display: flex;
  margin-top: 10rpx;
}

.image-preview {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
  margin-right: 20rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.delete-icon {
  position: absolute;
  top: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button {
  width: 200rpx;
  height: 200rpx;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9f9f9;
}

.upload-icon {
  font-size: 60rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #999;
}

.upload-tips {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

/* 提交按钮 */
.submit-button {
  margin: 40rpx 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 12rpx rgba(212, 56, 13, 0.2);
}

.submit-button.disabled {
  opacity: 0.6;
}
