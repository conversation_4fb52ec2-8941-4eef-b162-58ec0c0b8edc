/* pages/splash/splash.wxss */
.splash-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background-color: #FFF7F7;
  padding: 0 50rpx 50rpx;
  box-sizing: border-box;
  position: relative;
}

/* 顶部状态栏占位 */
.status-bar {
  width: 100%;
  height: 44px; /* 根据实际设备调整 */
}

/* 中间标语样式 */
.splash-slogan {
  width: 100%;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.slogan-icon {
  height: 300rpx;
  width: 300rpx;
  margin-top: -200rpx;
  margin-bottom: 30rpx;
}

.slogan-text {
  font-weight: bold;
  letter-spacing: 2rpx;
  line-height: 1.5;
  display: block;
}

.slogan-text.primary {
  color: #4A1010;
  margin-bottom: 20rpx;
  font-size: 48rpx;
}

.slogan-text.secondary {
  color: #5c2424;
  font-size: 42rpx;
}

/* 立即进入按钮 */
.enter-button {
  width: 80%;
  height: 90rpx;
  background: linear-gradient(to right, #4A1010, #301515);
  color: #FFFFFF;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 45rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 100rpx;
  box-shadow: 0 4rpx 10rpx rgba(255, 77, 79, 0.2);
}

/* 底部logo */
.bottom-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.logo-text-en {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 10rpx;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-circle {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #4A1010;
  color: #FFFFFF;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10rpx;
}

.logo-text {
  font-size: 24rpx;
  color: #666666;
}
