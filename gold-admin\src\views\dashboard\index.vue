<template>
  <div class="dashboard-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="panel-group">
      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('newVisitis')">
          <div class="card-panel-icon-wrapper icon-people">
            <el-icon class="card-panel-icon"><User /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">用户总数</div>
            <div class="card-panel-num">{{ dashboardData.totalUsers }}</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('messages')">
          <div class="card-panel-icon-wrapper icon-message">
            <el-icon class="card-panel-icon"><Document /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">订单总数</div>
            <div class="card-panel-num">{{ dashboardData.totalOrders }}</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('purchases')">
          <div class="card-panel-icon-wrapper icon-money">
            <el-icon class="card-panel-icon"><Money /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">今日交易额</div>
            <div class="card-panel-num">¥{{ dashboardData.todayAmount }}</div>
          </div>
        </div>
      </el-col>

      <el-col :xs="12" :sm="12" :lg="6" class="card-panel-col">
        <div class="card-panel" @click="handleSetLineChartData('shoppings')">
          <div class="card-panel-icon-wrapper icon-shopping">
            <el-icon class="card-panel-icon"><Goods /></el-icon>
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">产品总数</div>
            <div class="card-panel-num">{{ dashboardData.totalProducts }}</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>订单趋势</span>
            </div>
          </template>
          <div class="chart-container">
            <line-chart :chart-data="lineChartData" />
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="24" :lg="12">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>订单状态分布</span>
            </div>
          </template>
          <div class="chart-container">
            <pie-chart :chart-data="pieChartData" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 今日最新订单 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>今日最新订单</span>
          <el-button type="primary" size="small" @click="$router.push('/orders')">
            查看更多
          </el-button>
        </div>
      </template>

      <el-table :data="recentOrders" style="width: 100%" v-loading="ordersLoading">
        <el-table-column prop="orderId" label="订单号" width="180" />
        <el-table-column prop="account" label="用户账号" width="120" />
        <el-table-column prop="goldType" label="贵金属类型" width="100">
          <template #default="scope">
            {{ getGoldTypeText(scope.row.goldType) }}
          </template>
        </el-table-column>
        <el-table-column prop="estimatedPrice" label="预估价格" width="120">
          <template #default="scope">
            ¥{{ scope.row.estimatedPrice }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间">
          <template #default="scope">
            {{ formatTime(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 无数据提示 -->
      <div v-if="!ordersLoading && recentOrders.length === 0" class="no-data">
        <el-empty description="今日暂无订单" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { parseTime } from '@/utils'
import { getOrderList } from '@/api/orders'
import { ElMessage } from 'element-plus'
import LineChart from './components/LineChart.vue'
import PieChart from './components/PieChart.vue'

const dashboardData = ref({
  totalUsers: 0,
  totalOrders: 0,
  todayAmount: 0,
  totalProducts: 0
})

const lineChartData = ref({
  newVisitis: {
    expectedData: [100, 120, 161, 134, 105, 160, 165],
    actualData: [120, 82, 91, 154, 162, 140, 145]
  },
  messages: {
    expectedData: [200, 192, 120, 144, 160, 130, 140],
    actualData: [180, 160, 151, 106, 145, 150, 130]
  },
  purchases: {
    expectedData: [80, 100, 121, 104, 105, 90, 100],
    actualData: [120, 90, 100, 138, 142, 130, 130]
  },
  shoppings: {
    expectedData: [130, 140, 141, 142, 145, 150, 160],
    actualData: [120, 82, 91, 154, 162, 140, 130]
  }
})

const pieChartData = ref([
  { value: 335, name: '待处理' },
  { value: 310, name: '处理中' },
  { value: 234, name: '已完成' },
  { value: 135, name: '已取消' }
])

const recentOrders = ref([])
const ordersLoading = ref(false)

function handleSetLineChartData(type) {
  // 这里可以根据类型切换图表数据
  console.log('切换图表数据:', type)
}

function getStatusType(status) {
  const statusMap = {
    0: 'danger',     // 已取消
    1: 'primary',    // 已下单
    2: 'warning',    // 已取件
    3: 'info',       // 待检测
    4: 'success',    // 已检测
    5: 'primary',    // 已确认
    6: 'success'     // 订单已完成
  }
  return statusMap[status] || 'info'
}

function getStatusText(status) {
  const statusMap = {
    0: '已取消',
    1: '已下单',
    2: '已取件',
    3: '待检测',
    4: '已检测',
    5: '已确认',
    6: '订单已完成'
  }
  return statusMap[status] || '未知'
}

function getGoldTypeText(goldType) {
  const typeMap = {
    'jewelry': '黄金',
    'bar': '铂金',
    'broken': '钯金',
    'other': '其他'
  }
  return typeMap[goldType] || goldType
}

function formatTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}')
}

// 获取今日最新订单
async function fetchTodayOrders() {
  ordersLoading.value = true
  try {
    // 获取当前日期
    const today = new Date()
    const todayStr = today.getFullYear() + '-' +
                    String(today.getMonth() + 1).padStart(2, '0') + '-' +
                    String(today.getDate()).padStart(2, '0')

    // 调用订单列表API
    const params = {
      pageNum: 1,
      pageSize: 10, // 只获取前10条
    }

    const response = await getOrderList(params)

    let allOrders = []

    // 适配后端返回的IPage结构
    if (response.data) {
      if (response.data.records) {
        allOrders = response.data.records || []
      } else if (response.data.list) {
        allOrders = response.data.list || []
      } else if (Array.isArray(response.data)) {
        allOrders = response.data
      }
    }

    // 过滤今日创建的订单
    const todayOrders = allOrders.filter(order => {
      if (!order.createTime) return false

      // 处理不同的时间格式
      let orderDate
      if (typeof order.createTime === 'string') {
        // 如果是字符串格式，如 '2025-06-04T13:47:17'
        orderDate = new Date(order.createTime)
      } else if (typeof order.createTime === 'number') {
        // 如果是时间戳
        orderDate = new Date(order.createTime)
      } else {
        return false
      }

      // 获取订单创建日期字符串
      const orderDateStr = orderDate.getFullYear() + '-' +
                           String(orderDate.getMonth() + 1).padStart(2, '0') + '-' +
                           String(orderDate.getDate()).padStart(2, '0')

      return orderDateStr === todayStr
    })

    // 按创建时间倒序排列，取前5条
    recentOrders.value = todayOrders
      .sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
      .slice(0, 5)

    console.log('今日订单过滤结果:', {
      todayStr,
      totalOrders: allOrders.length,
      todayOrders: todayOrders.length,
      displayOrders: recentOrders.value.length
    })

  } catch (error) {
    console.error('获取今日订单失败:', error)
    ElMessage.error('获取今日订单失败')

    // 开发环境下显示模拟数据
    if (process.env.NODE_ENV === 'development') {
      const today = new Date()
      recentOrders.value = [
        {
          orderId: 'REC' + today.getFullYear() + String(today.getMonth() + 1).padStart(2, '0') + String(today.getDate()).padStart(2, '0') + '001',
          account: 'user001',
          goldType: 'jewelry',
          estimatedPrice: 1500.00,
          status: 1,
          createTime: today.toISOString()
        },
        {
          orderId: 'REC' + today.getFullYear() + String(today.getMonth() + 1).padStart(2, '0') + String(today.getDate()).padStart(2, '0') + '002',
          account: 'user002',
          goldType: 'bar',
          estimatedPrice: 2800.00,
          status: 2,
          createTime: new Date(today.getTime() - 3600000).toISOString()
        }
      ]
    }
  } finally {
    ordersLoading.value = false
  }
}

async function fetchDashboardData() {
  try {
    // 这里应该调用实际的API
    // const response = await getDashboardStats()

    // 模拟数据
    dashboardData.value = {
      totalUsers: 1234,
      totalOrders: 567,
      todayAmount: 12345.67,
      totalProducts: 89
    }

  } catch (error) {
    console.error('获取仪表盘数据失败:', error)
  }
}

onMounted(() => {
  fetchDashboardData()
  fetchTodayOrders()
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
}

.panel-group {
  margin-bottom: 20px;
}

.card-panel-col {
  margin-bottom: 32px;
}

.chart-container {
  height: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-data {
  padding: 20px;
  text-align: center;
}
</style>
