/* pages/index/index.wxss */
page {
  --primary-color: #8B4513; /* 棕色 */
  --secondary-color: #A0522D; /* 深棕色 */
  --light-color: #D2B48C; /* 浅棕色 */
  --accent-color: #CD853F; /* 秘鲁棕色 */
  --red-color: #E53935; /* 红色 */
  --background-color: #F5F5F5; /* 背景色 */
  --text-color: #333333; /* 文本色 */
  --light-text: #666666; /* 浅色文本 */
  --border-color: #EEEEEE; /* 边框色 */
  background-color: var(--background-color);
}
.container {
  background-color: #FFF7F7;
}



/* 金价展示区域 */
.gold-price-card {
  background: linear-gradient(to bottom, #4A1010, #FFFFFF);
  padding: 30rpx 20rpx;
  color: #FFFFFF;
  margin-left: -20rpx;
  margin-right: -20rpx;
}

.gold-price-title {
  font-size: 34rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.service-tags {
  display: flex;
  justify-content: space-around;
  margin-bottom: 20rpx;
}

.service-tag {
  display: flex;
  align-items: center;
}

.tag-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 6rpx;
}

.service-tag .tag-text {
  font-size: 24rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.price-info-container {
  display: flex;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10rpx;
  padding: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.platform-price {
  flex: 1;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  padding-right: 15rpx;
}

.price-label-1 {
  color: #ffffff;
  font-size: 24rpx;
  opacity: 0.9;
  margin-bottom: 15rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFFFFF;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.city-price {
  flex: 1;
  padding-left: 15rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 80rpx; /* 设置固定高度，确保轮播区域大小一致 */
  overflow: hidden;
}

.price-swiper {
  height: 100%;
  width: 100%;
}

.price-item {
  font-size: 24rpx;
  line-height: 1.5;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  height: 100%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 买卖入口 */
.trade-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.trade-button {
  flex: 1;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 8rpx;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: scale(1);
  position: relative;
  overflow: hidden;
}

.buy-button {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  color: #FFFFFF;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.4);
  border: none;
  animation: button-breathe 2.5s ease-in-out infinite;
}

.buy-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.buy-button.button-pressed {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(76, 175, 80, 0.6);
  animation-play-state: paused;
}

.buy-button.button-released {
  transform: scale(1.05);
  animation: button-bounce 0.3s ease-out;
}

@keyframes button-bounce {
  0% { transform: scale(1.05); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* 持续呼吸式动画 */
@keyframes button-breathe {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.sell-button {
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  color: #FFFFFF;
  box-shadow: 0 6rpx 20rpx rgba(255, 77, 79, 0.4);
  border: none;
  animation: button-breathe 2.5s ease-in-out infinite;
}

.sell-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.sell-button.button-pressed {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(255, 77, 79, 0.6);
  animation-play-state: paused;
}

.sell-button.button-released {
  transform: scale(1.05);
  animation: button-bounce 0.3s ease-out;
}

.receipt-options {
  display: flex;
  justify-content: space-between;
  background-color: rgba(255, 249, 196, 0.9);
  border-radius: 8rpx;
  padding: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-top: 10rpx;
}

.receipt-option {
  display: flex;
  align-items: center;
  flex: 1;
}

.lightning-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.receipt-option .option-text {
  font-size: 24rpx;
  color: #FF6F00;
}

/* 实时金价信息 */
.price-section {
  background-color: #FFF9F0;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.05);
}

.price-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.price-tag {
  width: 36rpx;
  height: 36rpx;
  background-color: #EEEEEE;
  border-radius: 6rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  font-weight: bold;
  margin-right: 10rpx;
}

.price-no {
  flex: 1;
  font-size: 24rpx;
  color: #999999;
}

.custom-price-button {
  padding: 6rpx 20rpx;
  background-color: #F5F5F5;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: var(--primary-color);
}

.real-price {
  margin-bottom: 20rpx;
  height: 311rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.price-label {
  color: #000000;
  font-size: 35rpx;
  opacity: 0.9;
  margin-bottom: 15rpx;
}

.price-amount {
  font-size: 90rpx;
  font-weight: bold;
  color: var(--red-color);
  margin-right: 10rpx;
}

.price-unit {
  font-size: 26rpx;
  color: var(--text-color);
  margin-right: 15rpx;
}

.price-change {
  font-size: 24rpx;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
}

.price-up {
  background-color: #f5f5f5;
  color: #FF4D4F;
}

.price-down {
  background-color: #f5f5f5;
  color: #52C41A;
}

.price-info {
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0;
  padding: 10rpx 0;
}

.price-info-item {
  flex: 1;
  display: flex;
  align-items: center;
}

.info-label {
  font-size: 26rpx;
  color: var(--light-text);
  margin-right: 10rpx;
}

.info-value {
  font-size: 26rpx;
  color: var(--text-color);
  font-weight: bold;
}

.payment-methods {
  margin-bottom: 15rpx;
}

.payment-title {
  font-size: 26rpx;
  color: var(--text-color);
  margin-bottom: 10rpx;
}

.payment-options {
  display: flex;
  justify-content: space-between;
}

.payment-option {
  display: flex;
  align-items: center;
  flex: 1;
}

.payment-icon {
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  margin-right: 10rpx;
}

.payment-icon.fast {
  background-color: #FFA000;
}

.payment-icon.instant {
  background-color: #F44336;
}

.payment-icon.normal {
  background-color: #9E9E9E;
}

.payment-method {
  font-size: 26rpx;
  color: var(--text-color);
  margin-right: 10rpx;
}

.payment-price {
  font-size: 26rpx;
  color: var(--primary-color);
  font-weight: bold;
}

.delay-price {
  font-size: 24rpx;
  color: var(--light-text);
  margin-bottom: 15rpx;
}

.small-text {
  font-size: 22rpx;
  color: #AAAAAA;
  margin-left: 10rpx;
  text-decoration: line-through;
}

.red-packet {
  display: flex;
  align-items: center;
  background-color: #FFF0F0;
  padding: 6rpx 15rpx;
  border-radius: 30rpx;
}

.red-packet-icon {
  width: 30rpx;
  height: 30rpx;
  margin-right: 6rpx;
}

.red-packet .packet-text {
  font-size: 22rpx;
  color: var(--red-color);
}

.days-left {
  background-color: var(--red-color);
  color: #FFFFFF !important;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-left: 6rpx;
}

/* 底部导航栏 */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background-color: #FFFFFF;
  display: flex;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 6rpx;
}

.tab-text {
  font-size: 22rpx;
  color: #999999;
}

.tab-item.active .tab-text {
  color: var(--primary-color);
}

.badge {
  position: absolute;
  top: 0;
  right: 50%;
  transform: translateX(12rpx);
  background-color: var(--red-color);
  color: #FFFFFF;
  font-size: 20rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 6rpx;
}

/* 市场行情区域 */
.market-section {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: rgb(0, 0, 0);
}

.unit-text {
  font-size: 24rpx;
  color: #999999;
}

/* 表格样式 */
.market-table {
  border-radius: 12rpx;
  overflow: hidden;
  border: 1rpx solid #f0f0f0;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e9ecef;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 20rpx 15rpx;
  text-align: center;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-header .table-cell {
  font-weight: bold;
  color: #495057;
  background-color: #f8f9fa;
}

.product-name {
  color: #333;
  font-weight: 500;
}

.buy-price {
  color: #28a745;
  font-weight: 500;
}

.sell-price {
  color: #dc3545;
  font-weight: 500;
}

.high-low {
  flex-direction: column;
  padding: 15rpx 10rpx;
}

.high-price {
  color: #dc3545;
  font-size: 24rpx;
  margin-bottom: 5rpx;
}

.low-price {
  color: #28a745;
  font-size: 24rpx;
}

/* 优惠服务区域 */
.discount-section {
  display: flex;
  justify-content: space-between;
  margin: 0 20rpx 20rpx;
}

.discount-card {
  width: 48%;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.small-card {
  background: linear-gradient(135deg, #F5DEB3, #DEB887);
}

.large-card {
  background: linear-gradient(135deg, #FFA07A, #FF7F50);
}

.discount-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  background-color: #FFFFFF;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: var(--primary-color);
  margin-bottom: 10rpx;
  align-self: flex-start;
}

.discount-title {
  font-size: 26rpx;
  color: #8B4513;
  margin-bottom: 10rpx;
}

.discount-amount {
  font-size: 30rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 15rpx;
}

.discount-limit {
  font-size: 24rpx;
  color: #8B4513;
  background-color: rgba(255, 255, 255, 0.5);
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  align-self: flex-start;
}

.highlight {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF4500;
}

/* 为什么选择金屋 */
.why-choose-section {
  margin: 0 0 20rpx;
  background-color: #FFFFFF;
  padding: 30rpx 0;
  position: relative;
}

.why-choose-section::before,
.why-choose-section::after,
.user-reviews-section::before,
.user-reviews-section::after {
  content: '';
  display: block;
  height: 1rpx;
  background: #EEEEEE;
  position: absolute;
  left: 20rpx;
  right: 20rpx;
}

.why-choose-section::before {
  top: 0;
}

.why-choose-section::after,
.user-reviews-section::before {
  bottom: 0;
}

.user-reviews-section::after {
  bottom: 0;
}

.why-choose-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 30rpx;
  text-align: center;
  position: relative;
}

.advantage-item {
  display: flex;
  margin: 0 20rpx 30rpx;
  padding: 20rpx;
  background-color: #FFF9F0;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.advantage-item:last-child {
  margin-bottom: 0;
}

.advantage-content {
  flex: 1;
  padding-right: 20rpx;
}

.advantage-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 10rpx;
}

.advantage-desc {
  font-size: 24rpx;
  color: #A0522D;
  line-height: 1.5;
}

.advantage-icon-container {
  width: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.advantage-icon {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(255,255,255,0.2));
  border: 1px solid rgba(205, 133, 63, 0.3);
  position: relative;
}

.advantage-icon image {
  width: 60rpx;
  height: 60rpx;
}

/* 用户评价 */
.user-reviews-section {
  margin: 0 0 20rpx;
  background-color: #FFFFFF;
  padding: 30rpx 0;
  position: relative;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 30rpx;
  text-align: center;
  position: relative;
}

.section-title::before,
.section-title::after {
  content: '';
  display: block;
  height: 1rpx;
  background: #EEEEEE;
  position: absolute;
  top: 50%;
  width: 80rpx;
}

.section-title::before {
  left: 30%;
}

.section-title::after {
  right: 30%;
}

/* 视频播放器样式 */
.video-container {
  margin: 0 20rpx 30rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #000;
  position: relative;
}

.video-container .video-player {
  width: 100%;
  height: 400rpx;
}

.video-title {
  position: absolute;
  left: 0;
  top: 0;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
  background: linear-gradient(to bottom, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0) 100%);
}

/* 用户评价列表样式 */
.reviews-list {
  padding: 0 20rpx;
}

.review-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
}

.review-item:last-child {
  border-bottom: none;
}

.review-user {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

/* 查看更多按钮 */
.view-more {
  margin-top: 20rpx;
  text-align: center;
  font-size: 26rpx;
  color: var(--light-text);
  padding: 15rpx 0;
  border-top: 1rpx solid var(--border-color);
}

.view-more .arrow {
  margin-left: 10rpx;
  font-size: 24rpx;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f5f5f5;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.user-location {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.review-date {
  font-size: 24rpx;
  color: #999;
}

.review-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.more-reviews {
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #8B4513;
}

.arrow {
  display: inline-block;
  margin-left: 6rpx;
}

/* 悬浮客服按钮样式 */
.floating-service-btn {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(255, 77, 79, 0.4);
  z-index: 999;
  transition: all 0.3s ease;
}

.floating-service-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 15rpx rgba(255, 77, 79, 0.6);
}

.service-icon {
  font-size: 48rpx;
  color: #fff;
  font-weight: bold;
  font-family: Arial, sans-serif;
}
