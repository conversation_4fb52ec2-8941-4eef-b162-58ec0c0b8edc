<!--pages/lockPrice/lockPrice.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <view class="user-name">{{isLogin ? (userInfo.username || userInfo.nickname || userInfo.account || '用户') : '未登录'}}</view>
    <block wx:if="{{isLogin}}">
      <view class="user-id">ID: {{userInfo.id || userInfo.account || ''}}</view>
      <view class="login-status">
        <view class="status-icon">✓</view>
        <view class="status-text">已登录</view>
      </view>
    </block>
    <block wx:else>
      <view class="login-status" bindtap="goToLogin">
        <view class="status-icon">!</view>
        <view class="status-text">点击登录</view>
      </view>
    </block>
  </view>

  <!-- 可用定金卡片 -->
  <view class="balance-card">
    <view class="balance-title">可用定金(元)</view>
    <view class="balance-amount">{{balance}}</view>
    <view class="balance-details">
      <view class="detail-item">
        <view class="detail-label">可用积分</view>
        <view class="detail-value">{{points}}</view>
      </view>
      <view class="divider"></view>
      <view class="detail-item">
        <view class="detail-label">黄金存料</view>
        <view class="detail-value">{{creditLimit}}</view>
      </view>
    </view>
  </view>

  <!-- 黄金价格信息 -->
  <view class="gold-info-card">
    <view class="gold-info-header">
      <view class="gold-icon">黄金</view>
      <view class="gold-inventory">库存: {{goldInventory}}</view>
    </view>
    <view class="gold-price-info">
      <view class="gold-price">{{goldPriceBuy}} / {{goldPriceSell}}</view>
    </view>
  </view>

  <!-- 锁价按钮区域 -->
  <view class="lock-buttons">
    <view class="lock-button buy-button" bindtap="lockBuy">
      <view class="lock-icon">买</view>
      <view class="lock-text">锁价买料</view>
    </view>
    <view class="lock-button sell-button" bindtap="lockSell">
      <view class="lock-icon">卖</view>
      <view class="lock-text">锁价卖料</view>
    </view>
  </view>

  <!-- 郑重提醒 -->
  <view class="reminder-section">
    <view class="reminder-title">郑重提醒：</view>
    <view class="reminder-content">
      <view class="reminder-item">-因我司需提前为您备料备款，下单后请及时关注实物料订单状态！</view>
      <view class="reminder-item">-杜绝一切非法及来路不明的实物料款，实物板料！</view>
      <view class="reminder-item">-杜绝不实际履行，反向操作及对冲！</view>
    </view>
  </view>

  <!-- 底部导航栏由app.json中的tabBar提供 -->
</view>