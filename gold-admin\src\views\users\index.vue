<template>
  <div class="app-container">
    <!-- 搜索筛选 -->
    <div class="filter-container">
      <el-input
        v-model="listQuery.keyword"
        placeholder="请输入用户名或账号"
        style="width: 200px;"
        class="filter-item"
        @keyup.enter="handleFilter"
      />
      <el-button
        class="filter-item"
        type="primary"
        icon="Search"
        @click="handleFilter"
      >
        搜索
      </el-button>
    </div>

    <!-- 表格 -->
    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中"
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>

      <el-table-column label="用户名" width="180">
        <template #default="scope">
          {{ scope.row.username }}
        </template>
      </el-table-column>

      <el-table-column label="账号" width="auto">
        <template #default="scope">
          {{ scope.row.account }}
        </template>
      </el-table-column>

      <el-table-column label="用户等级" width="250" align="center">
        <template #default="scope">
          <el-tag :type="getUserLevelType(scope.row.userLevel)">
            {{ getUserLevelText(scope.row.userLevel) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="260">
        <template #default="scope">
          {{ formatTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="380">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleUpdateLevel(scope.row)">
            修改等级
          </el-button>
          <el-button type="success" size="small" @click="handleViewRelation(scope.row)">
            查看关系
          </el-button>
          <el-button type="warning" size="small" @click="handleViewPassword(scope.row)">
            查看密码
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <!-- 修改用户等级对话框 -->
    <el-dialog
      title="修改用户等级"
      v-model="levelDialogVisible"
      width="400px"
    >
      <el-form
        ref="levelFormRef"
        :model="levelForm"
        label-width="80px"
      >
        <el-form-item label="用户等级" prop="userLevel">
          <el-select v-model="levelForm.userLevel" placeholder="请选择">
            <el-option label="未实名用户" :value="0" />
            <el-option label="VIP" :value="1" />
            <el-option label="SVIP" :value="2" />
            <el-option label="企业用户" :value="3" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="levelDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpdateUserLevel">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看用户关系对话框 -->
    <el-dialog
      :title="relationData.user ? `用户关系 - ${relationData.user.username}（${relationData.user.account}）` : '用户关系'"
      v-model="relationDialogVisible"
      width="600px"
    >
      <el-tabs v-model="activeTab">
        <el-tab-pane label="上上级" name="grandParent">
          <el-table :data="relationData.grandParent" border style="width: 100%">
            <el-table-column label="用户名" width="120">
              <template #default="scope">
                {{ scope.row.username || scope.row.account }}
              </template>
            </el-table-column>
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column label="用户等级" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getUserLevelType(scope.row.userLevel)">
                  {{ getUserLevelText(scope.row.userLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleDeleteRelation(scope.row.relationId)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="上级" name="parent">
          <el-table :data="relationData.parent" border style="width: 100%">
            <el-table-column label="用户名" width="120">
              <template #default="scope">
                {{ scope.row.username || scope.row.account }}
              </template>
            </el-table-column>
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column label="用户等级" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getUserLevelType(scope.row.userLevel)">
                  {{ getUserLevelText(scope.row.userLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleDeleteRelation(scope.row.relationId)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="下级" name="children">
          <el-table :data="relationData.children" border style="width: 100%">
            <el-table-column label="用户名" width="120">
              <template #default="scope">
                {{ scope.row.username || scope.row.account }}
              </template>
            </el-table-column>
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column label="用户等级" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getUserLevelType(scope.row.userLevel)">
                  {{ getUserLevelText(scope.row.userLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleDeleteRelation(scope.row.relationId)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="下下级" name="subChildren">
          <el-table :data="relationData.subChildren" border style="width: 100%">
            <el-table-column label="用户名" width="120">
              <template #default="scope">
                {{ scope.row.username || scope.row.account }}
              </template>
            </el-table-column>
            <el-table-column prop="account" label="账号" width="120" />
            <el-table-column label="用户等级" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getUserLevelType(scope.row.userLevel)">
                  {{ getUserLevelText(scope.row.userLevel) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" width="160">
              <template #default="scope">
                {{ formatTime(scope.row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" align="center">
              <template #default="scope">
                <el-button type="danger" size="small" @click="handleDeleteRelation(scope.row.relationId)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 密码管理对话框 -->
    <el-dialog
      :title="passwordData.user ? `密码管理 - ${passwordData.user.username}（${passwordData.user.account}）` : '密码管理'"
      v-model="passwordDialogVisible"
      width="500px"
      @close="resetPasswordForm"
    >
      <div v-if="passwordData.currentPassword">
        <el-form label-width="100px">
          <el-form-item label="当前密码">
            <div style="display: flex; align-items: center; gap: 10px;">
              <el-input
                :value="showPassword ? passwordData.currentPassword : '••••••••'"
                readonly
                style="flex: 1;"
              />
              <el-button
                :icon="showPassword ? 'Hide' : 'View'"
                @click="togglePasswordVisibility"
                size="small"
              >
                {{ showPassword ? '隐藏' : '显示' }}
              </el-button>
            </div>
          </el-form-item>
        </el-form>

        <el-divider content-position="left">修改密码</el-divider>
      </div>

      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码（6-20位）"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="passwordDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleUpdatePassword"
            :loading="passwordLoading"
          >
            修改密码
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList, updateUserLevel, getUserRelationTree, getUserRelations, getUserChildren, deleteUserRelation, getUserPassword, updateUserPassword } from '@/api/users'
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination/index.vue'

const list = ref([])
const total = ref(0)
const listLoading = ref(true)
const listQuery = ref({
  page: 1,
  limit: 10,
  keyword: ''
})

// 修改用户等级相关
const levelDialogVisible = ref(false)
const levelFormRef = ref()
const levelForm = ref({
  account: '',
  userLevel: 2
})

// 查看用户关系相关
const relationDialogVisible = ref(false)
const activeTab = ref('children')
const relationData = ref({
  user: null,
  parent: [],
  grandParent: [],
  children: [],
  subChildren: []
})

// 密码管理相关
const passwordDialogVisible = ref(false)
const passwordFormRef = ref()
const passwordLoading = ref(false)
const showPassword = ref(false)
const passwordData = ref({
  user: null,
  currentPassword: ''
})
const passwordForm = ref({
  newPassword: '',
  confirmPassword: ''
})

// 密码表单验证规则
const passwordRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

async function getList() {
  listLoading.value = true
  try {
    const response = await getUserList(listQuery.value)
    if (response.code === 200) {
      list.value = response.data
      total.value = response.data.length
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    listLoading.value = false
  }
}

function handleFilter() {
  listQuery.value.page = 1
  getList()
}

// 修改用户等级
function handleUpdateLevel(row) {
  levelForm.value = {
    account: row.account,
    userLevel: row.userLevel
  }
  levelDialogVisible.value = true
}

async function handleUpdateUserLevel() {
  try {
    // 先删除用户相关的关系
    await deleteUserRelations(levelForm.value.account)

    // 然后修改用户等级
    const response = await updateUserLevel(levelForm.value.account, levelForm.value.userLevel)
    if (response.code === 200) {
      ElMessage.success('修改成功')
      levelDialogVisible.value = false
      getList()
    } else {
      ElMessage.error(response.message || '修改失败')
    }
  } catch (error) {
    console.error('修改用户等级失败:', error)
    ElMessage.error('修改用户等级失败')
  }
}

// 删除用户相关的所有关系
async function deleteUserRelations(account) {
  try {
    // 获取用户的上级关系
    const parentRelations = await getUserRelations(account)
    if (parentRelations.code === 200 && parentRelations.data) {
      for (const relation of parentRelations.data) {
        await deleteUserRelation(relation.id)
      }
    }

    // 获取用户的下级关系
    const childRelations = await getUserChildren(account)
    if (childRelations.code === 200 && childRelations.data) {
      for (const relation of childRelations.data) {
        await deleteUserRelation(relation.id)
      }
    }
  } catch (error) {
    console.error('删除用户关系失败:', error)
    // 即使删除关系失败，也继续修改等级
  }
}

// 查看用户关系
async function handleViewRelation(row) {
  try {
    const response = await getUserRelationTree(row.account)
    if (response.code === 200) {
      // 处理API返回的数据结构，添加relationId字段
      const processRelationData = (items) => {
        // 确保items是数组
        const itemsArray = Array.isArray(items) ? items : (items ? [items] : [])
        return itemsArray.map(item => ({
          ...item,
          relationId: item.id, // 添加relationId字段用于删除操作
          username: item.username || item.account,
          userLevel: item.userLevel || 0,
          createTime: item.createTime
        }))
      }

      relationData.value = {
        user: response.data.user || row,
        parent: processRelationData(response.data.parent),
        grandParent: processRelationData(response.data.grandParent),
        children: processRelationData(response.data.children),
        subChildren: processRelationData(response.data.subChildren)
      }
      relationDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取用户关系失败')
    }
  } catch (error) {
    console.error('获取用户关系失败:', error)
    ElMessage.error('获取用户关系失败')
  }
}

// 删除用户关系
async function handleDeleteRelation(relationId) {
  try {
    await ElMessageBox.confirm('确定要删除这个用户关系吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await deleteUserRelation(relationId)
    if (response.code === 200) {
      ElMessage.success('删除成功')
      // 重新获取关系数据
      const currentUser = relationData.value.user
      if (currentUser) {
        handleViewRelation(currentUser)
      }
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户关系失败:', error)
      ElMessage.error('删除用户关系失败')
    }
  }
}

// 查看用户密码
async function handleViewPassword(row) {
  try {
    const response = await getUserPassword(row.account)
    if (response.code === 200) {
      passwordData.value = {
        user: row,
        currentPassword: response.data || ''
      }
      // 重置表单
      resetPasswordForm()
      passwordDialogVisible.value = true
    } else {
      ElMessage.error(response.message || '获取密码失败')
    }
  } catch (error) {
    console.error('获取用户密码失败:', error)
    ElMessage.error('获取用户密码失败')
  }
}

// 修改用户密码
async function handleUpdatePassword() {
  if (!passwordFormRef.value) return

  try {
    await passwordFormRef.value.validate()

    await ElMessageBox.confirm('确定要修改该用户的密码吗？', '确认修改', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    passwordLoading.value = true
    const response = await updateUserPassword(passwordData.value.user.account, passwordForm.value.newPassword)

    if (response.code === 200) {
      ElMessage.success('密码修改成功')
      passwordDialogVisible.value = false
      getList() // 刷新用户列表
    } else {
      ElMessage.error(response.message || '密码修改失败')
    }
  } catch (error) {
    if (error !== 'cancel' && error !== false) { // 不是取消操作和表单验证错误
      console.error('修改密码失败:', error)
      ElMessage.error('修改密码失败')
    }
  } finally {
    passwordLoading.value = false
  }
}

// 切换密码显示/隐藏
function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

// 重置密码表单
function resetPasswordForm() {
  if (passwordFormRef.value) {
    passwordFormRef.value.resetFields()
  }
  passwordForm.value = {
    newPassword: '',
    confirmPassword: ''
  }
  showPassword.value = false
  passwordLoading.value = false
}

function formatTime(time) {
  if (!time) return ''
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

function getUserLevelType(level) {
  const types = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return types[level] || 'info'
}

function getUserLevelText(level) {
  const texts = {
    0: '未实名用户',
    1: 'VIP',
    2: 'SVIP',
    3: '企业用户'
  }
  return texts[level] || '未实名用户'
}

onMounted(() => {
  getList()
})
</script>

<style lang="scss" scoped>
.filter-container {
  padding-bottom: 10px;
  .filter-item {
    margin-right: 10px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
