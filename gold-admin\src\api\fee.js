import request from '@/utils/request'

/**
 * 获取手续费配置
 */
export function getFeeConfig() {
  return request({
    url: '/admin/fee/config',
    method: 'get'
  })
}

/**
 * 更新手续费配置
 * @param {string} type - 手续费类型 (withdraw/trade)
 * @param {object} data - 配置数据
 */
export function updateFeeConfig(type, data) {
  return request({
    url: `/admin/fee/config/${type}`,
    method: 'put',
    data
  })
}

/**
 * 获取手续费统计
 */
export function getFeeStats() {
  return request({
    url: '/admin/fee/stats',
    method: 'get'
  })
}

/**
 * 获取手续费记录
 * @param {object} params - 查询参数
 */
export function getFeeRecords(params) {
  return request({
    url: '/admin/fee/records',
    method: 'get',
    params
  })
}

/**
 * 导出手续费记录
 * @param {object} params - 查询参数
 */
export function exportFeeRecords(params) {
  return request({
    url: '/admin/fee/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取所有用户等级手续费信息
 */
export function getPlatformFees() {
  return request({
    url: '/api/platform-fees',
    method: 'get'
  })
}

/**
 * 设置平台手续费
 * @param {object} feeData - 手续费数据
 * @param {string} feeData.account - 用户账号
 * @param {number} feeData.category - 类别 (0=黄金, 1=铂金, 2=钯金)
 * @param {number} feeData.feeRate - 手续费比例
 * @param {number} feeData.level - 用户等级
 * @param {string} feeData.levelName - 等级名称
 */
export function setPlatformFee(feeData) {
  return request({
    url: '/api/platform-fees',
    method: 'post',
    data: feeData
  })
}
