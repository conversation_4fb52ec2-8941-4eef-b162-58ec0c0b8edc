/* pages/product/detail.wxss */
.container {
  padding-bottom: 120rpx;
}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx;
  position: relative;
}

.product-image {
  width: 100%;
  height: 100%;
}

.swiper-indicator {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 24rpx;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
}

/* 商品信息卡片 */
.product-info-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-price {
  font-size: 48rpx;
  font-weight: bold;
  color: #FF6B00;
}

.product-tags {
  display: flex;
}

.product-tag {
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  color: #fff;
  margin-left: 10rpx;
}

.product-tag.hot {
  background-color: #FF6B00;
}

.product-tag.new {
  background-color: #07C160;
}

.product-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.product-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.product-meta-item {
  margin-right: 30rpx;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.meta-label {
  color: #999;
}

.gold-price-tip {
  font-size: 26rpx;
  color: #FF6B00;
  background-color: #FFF9E6;
  padding: 16rpx;
  border-radius: 8rpx;
}

/* 商品数量卡片 */
.product-quantity-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-title {
  font-size: 28rpx;
  color: #333;
}

/* 自定义步进器 */
.quantity-stepper {
  display: flex;
  align-items: center;
  height: 60rpx;
  border: 1rpx solid #eee;
  border-radius: 6rpx;
}

.stepper-minus, .stepper-plus {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  color: #333;
  background-color: #f5f5f5;
}

.stepper-minus.disabled, .stepper-plus.disabled {
  color: #ccc;
}

.stepper-input {
  width: 80rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border-left: 1rpx solid #eee;
  border-right: 1rpx solid #eee;
}

/* 商品详情卡片 */
.product-detail-card {
  background-color: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.detail-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: var(--primary-color);
  border-radius: 4rpx;
}

.detail-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  display: flex;
  height: 100rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.bottom-actions {
  display: flex;
  width: 40%;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: normal;
  border-radius: 0;
  font-size: inherit;
  color: inherit;
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 6rpx;
}

.action-text {
  font-size: 20rpx;
  color: #666;
}

.share-btn {
  position: relative;
}

.share-btn::after {
  border: none;
}

.bottom-buttons {
  width: 60%;
}

.buy-button {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #FFD700, #FFC107);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 分享面板 */
.share-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
}

.share-modal.show {
  visibility: visible;
}

.share-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.share-modal.show .share-mask {
  opacity: 1;
}

.share-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.share-modal.show .share-content {
  transform: translateY(0);
}

.share-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.share-title {
  font-size: 32rpx;
  font-weight: bold;
}

.share-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.share-body {
  padding: 30rpx;
}

.share-image-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30rpx;
}

.share-image {
  width: 400rpx;
  height: 400rpx;
  border: 1rpx solid #f0f0f0;
}

.share-tip {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.share-code {
  text-align: center;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.share-commission-tip {
  text-align: center;
  font-size: 26rpx;
  color: #FF6B00;
  margin-bottom: 30rpx;
  background-color: #FFF9E6;
  padding: 16rpx;
  border-radius: 8rpx;
}

.share-save-button {
  background: linear-gradient(135deg, #FFD700, #FFC107);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
}
