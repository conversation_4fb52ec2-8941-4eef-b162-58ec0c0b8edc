import request from '@/utils/request'

/**
 * 获取商城订单列表
 * @param {object} params - 查询参数
 */
export function getMallOrderList(params) {
  return request({
    url: '/admin/mall-orders/list',
    method: 'get',
    params
  })
}

/**
 * 获取商城订单详情
 * @param {number} id - 订单ID
 */
export function getMallOrderDetail(id) {
  return request({
    url: `/admin/mall-orders/${id}`,
    method: 'get'
  })
}

/**
 * 更新商城订单状态
 * @param {number} id - 订单ID
 * @param {object} data - 更新数据
 */
export function updateMallOrderStatus(id, data) {
  return request({
    url: `/admin/mall-orders/${id}/status`,
    method: 'put',
    data
  })
}

/**
 * 商城订单发货
 * @param {number} id - 订单ID
 * @param {object} data - 发货信息
 */
export function shipMallOrder(id, data) {
  return request({
    url: `/admin/mall-orders/${id}/ship`,
    method: 'put',
    data
  })
}

/**
 * 商城订单退款
 * @param {number} id - 订单ID
 */
export function refundMallOrder(id) {
  return request({
    url: `/admin/mall-orders/${id}/refund`,
    method: 'put'
  })
}

/**
 * 导出商城订单
 * @param {object} params - 查询参数
 */
export function exportMallOrders(params) {
  return request({
    url: '/admin/mall-orders/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取商城订单统计
 */
export function getMallOrderStats() {
  return request({
    url: '/admin/mall-orders/stats',
    method: 'get'
  })
}
