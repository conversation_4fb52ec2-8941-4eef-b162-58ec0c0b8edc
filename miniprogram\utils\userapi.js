/**
 * 用户相关API请求封装
 */

const app = getApp();

/**
 * 获取完整的API URL
 * @param {string} path - API路径
 * @returns {string} 完整的API URL
 */
const getFullUrl = (path) => {
  return `${app.globalData.apiConfig.baseUrl}${path}`;
};

/**
 * 统一处理请求
 * @param {Object} options - 请求选项
 * @returns {Promise} Promise对象
 */
const request = (options) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': options.contentType || 'application/json',
        'Authorization': options.token ? `Bearer ${options.token}` : ''
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data);
        } else {
          reject({
            code: res.statusCode,
            msg: res.data.msg || '请求失败'
          });
        }
      },
      fail: (err) => {
        reject({
          code: -1,
          msg: '网络错误，请检查网络连接',
          error: err
        });
      }
    });
  });
};

/**
 * 用户登录
 * @param {string} account - 账号
 * @param {string} password - 密码
 * @returns {Promise} Promise对象
 */
const login = (account, password) => {
  return request({
    url: getFullUrl(app.globalData.apiConfig.user.login),
    method: 'POST',
    data: {
      account,
      password
    }
  });
};

/**
 * 用户注册
 * @param {Object} userData - 用户数据
 * @returns {Promise} Promise对象
 */
const register = (userData) => {
  return request({
    url: getFullUrl(app.globalData.apiConfig.user.register),
    method: 'POST',
    contentType: 'application/x-www-form-urlencoded',
    data: userData
  });
};

/**
 * 获取用户信息
 * @param {string} token - 用户令牌
 * @returns {Promise} Promise对象
 */
const getUserInfo = (token) => {
  return request({
    url: getFullUrl(app.globalData.apiConfig.user.info),
    token: token
  });
};

/**
 * 更新用户信息
 * @param {Object} userData - 用户数据
 * @param {string} token - 用户令牌
 * @returns {Promise} Promise对象
 */
const updateUserInfo = (userData, token) => {
  return request({
    url: getFullUrl(app.globalData.apiConfig.user.update),
    method: 'POST',
    data: userData,
    token: token
  });
};

/**
 * 重置密码
 * @param {string} oldPassword - 旧密码
 * @param {string} newPassword - 新密码
 * @param {string} token - 用户令牌
 * @returns {Promise} Promise对象
 */
const resetPassword = (oldPassword, newPassword, token) => {
  return request({
    url: getFullUrl(app.globalData.apiConfig.user.resetPassword),
    method: 'POST',
    data: {
      oldPassword,
      newPassword
    },
    token: token
  });
};

/**
 * 保存用户信息到本地和全局
 * @param {Object} userInfo - 用户信息
 */
const saveUserInfo = (userInfo) => {
  // 添加登录状态标记
  const userData = {
    ...userInfo,
    isLogin: true
  };
  
  // 保存到全局
  app.globalData.userInfo = userData;
  
  // 保存到本地存储
  wx.setStorageSync('userInfo', userData);
};

/**
 * 退出登录
 */
const logout = () => {
  // 清除全局用户信息
  app.globalData.userInfo = null;
  
  // 清除本地存储的用户信息
  wx.removeStorageSync('userInfo');
};

module.exports = {
  login,
  register,
  getUserInfo,
  updateUserInfo,
  resetPassword,
  saveUserInfo,
  logout
};
