<!--pages/customer/index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrap">
      <input class="search-input" placeholder="搜索客户昵称/手机号" bindinput="onSearchInput" value="{{searchKeyword}}" />
      <view class="search-icon" wx:if="{{!searchKeyword}}">
        <image class="icon" src="/images/icons/search.png"></image>
      </view>
      <view class="clear-icon" bindtap="clearSearch" wx:else>
        <text class="icon-text">×</text>
      </view>
    </view>
  </view>

  <!-- 客户统计 -->
  <view class="customer-stats">
    <view class="stats-item">
      <view class="stats-value">{{totalCustomers}}</view>
      <view class="stats-label">总客户数</view>
    </view>
    <view class="stats-item">
      <view class="stats-value">{{activeCustomers}}</view>
      <view class="stats-label">活跃客户</view>
    </view>
    <view class="stats-item">
      <view class="stats-value">{{totalPoints}}</view>
      <view class="stats-label">累计返积分</view>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tab-bar">
    <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" bindtap="switchTab" data-tab="all">
      <text class="tab-text">全部 ({{allCustomers.length}})</text>
    </view>
    <view class="tab-item {{currentTab === 'level1' ? 'active' : ''}}" bindtap="switchTab" data-tab="level1">
      <text class="tab-text">一级下级 ({{level1Customers.length}})</text>
    </view>
    <!-- 只有非SVIP用户才显示二级下级标签页 -->
    <view class="tab-item {{currentTab === 'level2' ? 'active' : ''}}" bindtap="switchTab" data-tab="level2" wx:if="{{userLevel !== 2}}">
      <text class="tab-text">二级下级 ({{level2Customers.length}})</text>
    </view>
  </view>

  <!-- 客户列表 -->
  <view class="customer-list" wx:if="{{filteredCustomers.length > 0}}">
    <view class="customer-item" wx:for="{{filteredCustomers}}" wx:key="id" bindtap="showCustomerDetail" data-id="{{item.id}}">
      <view class="customer-avatar">
        <image class="avatar" src="{{item.avatar || '/images/default-avatar.png'}}"></image>
      </view>
      <view class="customer-info">
        <view class="customer-name-level">
          <text class="customer-name">{{item.nickname || '客户' + item.id.slice(-4)}}</text>
          <view class="customer-level {{item.level === 1 ? 'level1' : 'level2'}}">
            {{item.level === 1 ? '一级下级' : '二级下级'}}
          </view>
        </view>
        <view class="customer-meta">
          <text class="customer-phone">{{item.phone || '未绑定手机'}}</text>
          <text class="customer-time">注册：{{item.registerTime}}</text>
        </view>
        <view class="customer-stats">
          <view class="customer-stat-item">
            <text class="stat-label">用户ID：</text>
            <text class="stat-value">{{item.userId}}</text>
          </view>
          <view class="customer-stat-item">
            <text class="stat-label">邀请人：</text>
            <text class="stat-value">{{item.inviter || '无'}}</text>
          </view>
        </view>
      </view>
      <view class="customer-arrow">
        <text class="arrow-text">></text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-container" wx:else>
    <image class="empty-icon" src="/images/icons/empty-customer.png"></image>
    <text class="empty-text">暂无客户数据</text>
  </view>

  <!-- 客户详情弹窗 -->
  <view class="customer-modal {{showDetail ? 'show' : ''}}">
    <view class="modal-mask" bindtap="hideCustomerDetail"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">客户详情</view>
        <view class="modal-close" bindtap="hideCustomerDetail">×</view>
      </view>
      <view class="modal-body">
        <view class="detail-header">
          <image class="detail-avatar" src="{{currentCustomer.avatar || '/images/default-avatar.png'}}"></image>
          <view class="detail-basic">
            <view class="detail-name">{{currentCustomer.nickname || '客户' + currentCustomer.id.slice(-4)}}</view>
            <view class="detail-level {{currentCustomer.level === 1 ? 'level1' : 'level2'}}">
              {{currentCustomer.level === 1 ? '一级下级' : '二级下级'}}
            </view>
            <view class="detail-phone">{{currentCustomer.phone || '未绑定手机'}}</view>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">基本信息</view>
          <view class="detail-item">
            <text class="detail-label">用户ID</text>
            <text class="detail-value">{{currentCustomer.userId || '未知'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">注册时间</text>
            <text class="detail-value">{{currentCustomer.registerTime || '未知'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">最近更新</text>
            <text class="detail-value">{{currentCustomer.lastActiveTime || '未知'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">邀请人</text>
            <text class="detail-value">{{currentCustomer.inviter || '无'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">关系ID</text>
            <text class="detail-value">{{currentCustomer.relationId || '未知'}}</text>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">统计信息</view>
          <view class="detail-item">
            <text class="detail-label">累计消费</text>
            <text class="detail-value">¥{{currentCustomer.totalSpent || '0.00'}}</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">累计订单</text>
            <text class="detail-value">{{currentCustomer.orderCount || '0'}}笔</text>
          </view>
          <view class="detail-item">
            <text class="detail-label">累计积分</text>
            <text class="detail-value highlight">{{currentCustomer.points || '0'}}</text>
          </view>
        </view>

        <view class="detail-section">
          <view class="section-title">最近订单</view>
          <view class="order-list" wx:if="{{currentCustomer.recentOrders && currentCustomer.recentOrders.length > 0}}">
            <view class="order-item" wx:for="{{currentCustomer.recentOrders}}" wx:key="id">
              <view class="order-info">
                <view class="order-id">订单号：{{item.id}}</view>
                <view class="order-time">{{item.createTime}}</view>
              </view>
              <view class="order-amount">¥{{item.amount}}</view>
            </view>
          </view>
          <view class="no-order" wx:else>
            <text>暂无订单记录</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
