<template>
  <div class="app-container">
    <div class="fee-header">
      <h2>用户等级手续费管理</h2>
      <p>管理不同用户等级的手续费设置</p>
    </div>

    <!-- 用户等级手续费管理 -->
    <el-card class="platform-fee-card">
      <template #header>
        <div class="card-header">
          <span>用户等级手续费列表</span>
        </div>
      </template>

      <!-- 搜索筛选 -->
      <div class="filter-container" style="margin-bottom: 20px;">
        <el-input
          v-model="platformFeeQuery.keyword"
          placeholder="请输入用户等级、等级名称或类别"
          style="width: 250px;"
          class="filter-item"
          @keyup.enter="handlePlatformFeeFilter"
        />
        <el-button
          class="filter-item"
          type="primary"
          icon="Search"
          @click="handlePlatformFeeFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          icon="Refresh"
          @click="handlePlatformFeeReset"
        >
          重置
        </el-button>
      </div>

      <!-- 用户等级手续费表格 -->
      <el-table
        v-loading="platformFeeLoading"
        :data="filteredPlatformFees"
        border
        style="width: 100%"
        :default-sort="{ prop: 'category', order: 'ascending' }"
      >
        <el-table-column prop="level" label="用户等级" width="120" align="center" sortable />
        <el-table-column prop="levelName" label="等级名称" width="150" />
        <el-table-column prop="category" label="类别" width="120" align="center" sortable>
          <template #default="scope">
            {{ categoryMap[scope.row.category] || '未知' }}
          </template>
        </el-table-column>
        <el-table-column prop="feeRate" label="手续费（元/克）" width="150" align="center" sortable>
          <template #default="scope">
            {{ formatFeeRate(scope.row.feeRate) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEditPlatformFee(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 用户等级手续费设置对话框 -->
    <el-dialog
      title="编辑用户等级手续费"
      v-model="platformFeeDialogVisible"
      width="500px"
    >
      <el-form
        ref="platformFeeFormRef"
        :model="platformFeeForm"
        :rules="platformFeeRules"
        label-width="120px"
      >
        <el-form-item label="用户等级">
          <el-input-number
            v-model="platformFeeForm.level"
            :min="1"
            :max="999"
            style="width: 100%"
            disabled
          />
        </el-form-item>
        <el-form-item label="等级名称">
          <el-input
            v-model="platformFeeForm.levelName"
            disabled
          />
        </el-form-item>
        <el-form-item label="类别" prop="category">
          <el-select
            v-model="platformFeeForm.category"
            placeholder="请选择类别"
            style="width: 100%"
          >
            <el-option
              v-for="option in categoryOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="手续费（元/克）" prop="feeRate">
          <el-input-number
            v-model="platformFeeForm.feeRate"
            :precision="2"
            :min="0"
            style="width: 100%"
            placeholder="请输入手续费"
          />
          <span class="unit">元/克</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="platformFeeDialogVisible = false">
            取消
          </el-button>
          <el-button type="primary" @click="confirmPlatformFee" :loading="platformFeeSubmitLoading">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { getPlatformFees, setPlatformFee } from '@/api/fee'

// 类别映射
const categoryMap = {
  0: '黄金',
  1: '铂金',
  2: '钯金'
}

const categoryOptions = [
  { label: '黄金', value: 0 },
  { label: '铂金', value: 1 },
  { label: '钯金', value: 2 }
]

// 用户等级手续费相关
const platformFeeLoading = ref(false)
const platformFees = ref([])
const platformFeeQuery = ref({
  keyword: ''
})
const platformFeeDialogVisible = ref(false)
const platformFeeSubmitLoading = ref(false)
const platformFeeForm = ref({
  account: '',
  category: null,
  feeRate: null,
  level: null,
  levelName: ''
})

// 过滤后的平台手续费数据
const filteredPlatformFees = computed(() => {
  let result = platformFees.value

  // 先过滤
  if (platformFeeQuery.value.keyword) {
    const keyword = platformFeeQuery.value.keyword.toLowerCase()
    result = result.filter(item =>
      item.level.toString().includes(keyword) ||
      item.levelName.toLowerCase().includes(keyword) ||
      categoryMap[item.category]?.toLowerCase().includes(keyword)
    )
  }

  // 再排序：先按类别排序，再按等级排序
  return result.sort((a, b) => {
    // 首先按类别排序 (0=黄金, 1=铂金, 2=钯金)
    if (a.category !== b.category) {
      return a.category - b.category
    }
    // 类别相同时，按等级排序
    return a.level - b.level
  })
})

const platformFeeRules = {
  category: [
    { required: true, message: '请选择类别', trigger: 'change' }
  ],
  feeRate: [
    { required: true, message: '请输入手续费', trigger: 'blur' },
    { type: 'number', min: 0, message: '手续费不能小于0', trigger: 'blur' }
  ]
}

// 用户等级手续费相关函数
async function loadPlatformFees() {
  platformFeeLoading.value = true
  try {
    const response = await getPlatformFees()
    console.log('API响应:', response)

    // 根据响应拦截器的处理，response就是后端返回的数据
    // 您提供的响应格式是直接返回数组
    if (Array.isArray(response)) {
      platformFees.value = response
    } else if (response && Array.isArray(response.data)) {
      platformFees.value = response.data
    } else {
      platformFees.value = []
      console.warn('响应数据格式不正确:', response)
    }

    console.log('获取用户等级手续费成功:', platformFees.value)
  } catch (error) {
    console.error('获取用户等级手续费失败:', error)
    ElMessage.error(`获取用户等级手续费失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)

    // 开发环境下显示模拟数据，使用您提供的响应格式
    if (process.env.NODE_ENV === 'development') {
      platformFees.value = [
        {
          level: 2,
          levelName: "11",
          feeRate: 10.00
        },
        {
          level: 1,
          levelName: "1级客户",
          feeRate: 20.00
        }
      ]
    }
  } finally {
    platformFeeLoading.value = false
  }
}

function handleEditPlatformFee(row) {
  platformFeeForm.value = {
    account: row.account || '',
    category: row.category,
    feeRate: row.feeRate,
    level: row.level,
    levelName: row.levelName
  }
  platformFeeDialogVisible.value = true
}

async function confirmPlatformFee() {
  // 验证表单
  if (platformFeeForm.value.category === null) {
    ElMessage.error('请选择类别')
    return
  }
  if (platformFeeForm.value.feeRate === null || platformFeeForm.value.feeRate < 0) {
    ElMessage.error('请输入有效的手续费')
    return
  }

  platformFeeSubmitLoading.value = true
  try {
    await setPlatformFee({
      account: platformFeeForm.value.account,
      category: platformFeeForm.value.category,
      feeRate: platformFeeForm.value.feeRate,
      level: platformFeeForm.value.level,
      levelName: platformFeeForm.value.levelName
    })

    ElMessage.success('更新成功')
    platformFeeDialogVisible.value = false
    loadPlatformFees()
  } catch (error) {
    console.error('设置用户等级手续费失败:', error)
    ElMessage.error(`操作失败: ${error.response?.data?.message || error.message || '请稍后重试'}`)
  } finally {
    platformFeeSubmitLoading.value = false
  }
}

function handlePlatformFeeFilter() {
  // 过滤功能通过computed属性实现，这里不需要额外操作
}

function handlePlatformFeeReset() {
  platformFeeQuery.value.keyword = ''
}

function formatFeeRate(rate) {
  // 直接显示原始值，因为后端返回的就是元/克的值
  if (rate === null || rate === undefined) {
    return '0.00'
  }
  return Number(rate).toFixed(2)
}

onMounted(() => {
  loadPlatformFees()
})
</script>

<style scoped>
.fee-header {
  margin-bottom: 20px;
}

.fee-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.fee-header p {
  margin: 0;
  color: #909399;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.platform-fee-card {
  margin-top: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  margin-right: 10px;
}

.dialog-footer {
  text-align: right;
}
</style>
