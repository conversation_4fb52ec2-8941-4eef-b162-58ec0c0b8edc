// pages/order/list.js
const app = getApp();
const { countdownManager } = require('../../utils/countdown');

Page({
  data: {
    tabs: [
      { id: 'all', name: '全部' },
      { id: 'pending', name: '待处理' },
      { id: 'confirming', name: '待确认' },
      { id: 'completed', name: '已完成' },
      { id: 'cancelled', name: '已取消' }
    ],
    currentTab: 'all',
    orders: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    total: 0
  },

  onLoad: function (options) {
    // 如果有状态参数，设置当前标签
    if (options.status) {
      this.setData({
        currentTab: options.status
      });
    }

    this.loadOrders(true);
  },

  onShow: function () {
    // 页面显示时检查是否需要刷新数据
    if (this.data.needRefresh) {
      this.loadOrders(true);
      this.setData({
        needRefresh: false
      });
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadOrders(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onHide: function () {
    // 页面隐藏时停止所有倒计时
    this.stopAllCountdowns();
  },

  onUnload: function () {
    // 页面卸载时停止所有倒计时
    this.stopAllCountdowns();
  },

  onReachBottom: function () {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrders(false);
    }
  },

  // 加载订单列表
  loadOrders: function (refresh) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        orders: []
      });
    }

    if (!this.data.hasMore || this.data.loading) {
      return Promise.resolve();
    }

    // 检查用户登录状态
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.isLogin || !userInfo.account) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return Promise.resolve();
    }

    this.setData({ loading: true });

    return new Promise((resolve) => {
      // 构建API请求参数
      let apiUrl = `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/account/${userInfo.account}`;
      let requestData = {
        pageNum: this.data.page,
        pageSize: this.data.pageSize
      };

      // 状态筛选 - 存储筛选信息用于客户端处理
      let clientFilterStatuses = null;
      if (this.data.currentTab !== 'all') {
        const statusMapping = this.getStatusMapping(this.data.currentTab);
        if (statusMapping.length === 1) {
          // 单状态可以直接传给API
          requestData.status = statusMapping[0];
        } else {
          // 多状态筛选在客户端处理，获取所有数据
          clientFilterStatuses = statusMapping;
        }
      }

      wx.request({
        url: apiUrl,
        method: 'GET',
        data: requestData,
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`,
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('订单列表API响应:', res);
          this.setData({ loading: false });

          if (res.data.code === 200 && res.data.data) {
            const responseData = res.data.data;
            const records = responseData.records || [];

            // 处理订单数据
            let processedOrders = records.map(order => this.processOrderItem(order));

            // 如果使用了多状态筛选（客户端筛选），需要过滤数据
            if (clientFilterStatuses) {
              processedOrders = processedOrders.filter(order =>
                clientFilterStatuses.includes(order.status)
              );
            }

            // 根据当前页数决定是追加还是替换数据
            const allOrders = this.data.page === 1
              ? processedOrders
              : [...this.data.orders, ...processedOrders];

            this.setData({
              orders: allOrders,
              total: responseData.total || 0,
              hasMore: responseData.current < responseData.pages,
              page: this.data.page + 1
            });
          } else {
            wx.showToast({
              title: res.data.message || '获取订单列表失败',
              icon: 'none'
            });
          }
          resolve();
        },
        fail: (error) => {
          console.error('获取订单列表失败:', error);
          this.setData({ loading: false });
          wx.showToast({
            title: '网络错误，请稍后重试',
            icon: 'none'
          });
          resolve();
        }
      });
    });
  },

  // 处理单条订单数据
  processOrderItem: function (order) {
    // 处理图片列表
    let imageList = [];

    // 优先使用 imageList 字段
    if (order.imageList && Array.isArray(order.imageList)) {
      imageList = order.imageList;
    }
    // 如果 imageList 不存在，尝试解析 imageBase64 字段
    else if (order.imageBase64) {
      try {
        // imageBase64 可能是字符串化的JSON数组
        if (typeof order.imageBase64 === 'string') {
          // 尝试解析JSON字符串
          const parsed = JSON.parse(order.imageBase64);
          if (Array.isArray(parsed)) {
            imageList = parsed;
          } else {
            // 如果不是数组，可能是单个URL字符串
            imageList = [order.imageBase64];
          }
        } else if (Array.isArray(order.imageBase64)) {
          imageList = order.imageBase64;
        }
      } catch (error) {
        console.error('解析图片数据失败:', error, order.imageBase64);
        // 解析失败时，尝试直接使用原始数据
        if (order.imageBase64) {
          imageList = [order.imageBase64];
        }
      }
    }

    // 过滤掉无效的图片URL
    imageList = imageList.filter(url => url && typeof url === 'string' && url.trim() !== '');

    console.log('处理订单图片:', {
      orderId: order.orderId,
      originalImageBase64: order.imageBase64,
      originalImageList: order.imageList,
      processedImageList: imageList
    });

    const processedOrder = {
      id: order.id,
      orderId: order.orderId,
      account: order.account,
      goldType: order.goldType,
      goldTypeText: this.getGoldTypeText(order.goldType),
      goldCondition: order.goldCondition,
      purity: order.purity,
      estimatedWeight: this.formatWeight(order.estimatedWeight),
      estimatedPrice: this.formatPrice(order.estimatedPrice),
      finalPrice: this.formatPrice(order.finalPrice),
      quotationFinalAmount: '0.00', // 检测结果的最终金额
      status: order.status,
      statusText: this.getStatusText(order.status),
      description: order.description,
      inspectionResult: order.inspectionResult,
      receiverName: order.receiverName,
      receiverPhone: order.receiverPhone,
      receiverAddress: order.receiverAddress,
      imageBase64: order.imageBase64,
      imageList: imageList, // 处理后的图片列表
      hasImage: imageList.length > 0, // 是否有图片
      firstImage: imageList.length > 0 ? imageList[0] : '', // 第一张图片URL
      createTime: order.createTime,
      updateTime: order.updateTime,
      createTimeFormatted: this.formatTime(order.createTime),
      updateTimeFormatted: this.formatTime(order.updateTime),
      rawData: order // 保留原始数据以备后用
    };

    // 对于状态为4的订单，获取检测结果的最终价格并启动倒计时
    if (order.status === 4) {
      // 初始化倒计时相关字段
      processedOrder.countdownText = '';
      processedOrder.countdownExpired = false;
      processedOrder.quotationCreateTime = null;

      this.loadQuotationForOrder(processedOrder);
    }

    return processedOrder;
  },

  // 为订单加载检测结果的最终价格
  loadQuotationForOrder: function(orderItem) {
    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/quotations/order/${orderItem.orderId}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 200 && res.data.data) {
          const finalAmount = this.calculateFinalAmount(res.data.data);
          // 获取最新的quotation创建时间用于倒计时
          let quotationCreateTime = null;
          if (res.data.data.length > 0) {
            quotationCreateTime = res.data.data[0].createTime;
          }

          // 更新订单的检测结果最终金额和倒计时信息
          const orders = this.data.orders;
          const orderIndex = orders.findIndex(o => o.orderId === orderItem.orderId);
          if (orderIndex !== -1) {
            orders[orderIndex].quotationFinalAmount = finalAmount;
            orders[orderIndex].quotationCreateTime = quotationCreateTime;
            this.setData({ orders: orders });

            // 启动倒计时
            if (quotationCreateTime) {
              this.startCountdownForOrder(orderItem.orderId, quotationCreateTime);
            }
          }
        }
      },
      fail: (error) => {
        console.error('获取检测结果失败:', error);
      }
    });
  },

  // 计算最终金额（与detail页面逻辑一致）
  calculateFinalAmount: function(quotationDataArray) {
    if (!quotationDataArray || !Array.isArray(quotationDataArray) || quotationDataArray.length === 0) {
      return '0.00';
    }

    let totalAmount = 0;
    let totalShippingFee = 0;
    let totalInsuranceFee = 0;
    let totalServiceFee = 0;
    let totalOtherItems = 0;
    let totalVoucherAmount = 0;

    quotationDataArray.forEach(quotation => {
      // 计算当前报价记录的总价（所有检测项的 价格*纯金重 之和）
      const quotationTotal = (quotation.items || []).reduce((sum, item) => {
        const price = parseFloat(item.price) || 0;
        const pureGoldWeight = parseFloat(item.pureGoldWeight) || 0;
        return sum + (price * pureGoldWeight);
      }, 0);

      totalAmount += quotationTotal;
      totalShippingFee += parseFloat(quotation.shippingFee) || 0;
      totalInsuranceFee += parseFloat(quotation.insuranceFee) || 0;
      totalServiceFee += parseFloat(quotation.serviceFee) || 0;
      totalOtherItems += parseFloat(quotation.otherItems) || 0;
      totalVoucherAmount += parseFloat(quotation.voucherAmount) || 0;
    });

    const totalFees = totalShippingFee + totalInsuranceFee + totalServiceFee + totalOtherItems;
    const finalAmount = totalAmount - totalFees + totalVoucherAmount;

    return this.formatPrice(finalAmount);
  },

  // 启动订单倒计时
  startCountdownForOrder: function(orderId, createTime) {
    const countdownId = `order_${orderId}`;

    countdownManager.start(countdownId, createTime, (result) => {
      // 更新订单的倒计时显示
      const orders = this.data.orders;
      const orderIndex = orders.findIndex(o => o.orderId === orderId);
      if (orderIndex !== -1) {
        orders[orderIndex].countdownText = result.displayText;
        orders[orderIndex].countdownExpired = result.isExpired;
        this.setData({ orders: orders });
      }
    }, 15); // 15分钟倒计时
  },

  // 停止所有倒计时
  stopAllCountdowns: function() {
    countdownManager.stopAll();
  },

  // 获取状态映射
  getStatusMapping: function(tabId) {
    const statusMappings = {
      'pending': [1, 2, 3],    // 待处理：已下单、待取件、待检测
      'confirming': [4],       // 待确认：已检测
      'completed': [5, 6],     // 已完成：已确认、订单已完成
      'cancelled': [0]         // 已取消
    };
    return statusMappings[tabId] || [];
  },

  // 获取状态文字
  getStatusText: function (status) {
    const statusMap = {
      0: '已取消',
      1: '已下单',
      2: '待取件',
      3: '待检测',
      4: '已检测',
      5: '已确认',
      6: '订单已完成'
    };
    return statusMap[status] || '未知状态';
  },

  // 获取黄金类型文字
  getGoldTypeText: function (goldType) {
    const typeMap = {
      'jewelry': '黄金',
      'broken': '钯金',
      'bar': '铂金',
      'other': '其他'
    };
    return typeMap[goldType] || goldType || '未知类型';
  },

  // 格式化价格
  formatPrice: function (price) {
    if (price === null || price === undefined) return '0.00';
    return Number(price).toFixed(2);
  },

  // 格式化重量
  formatWeight: function (weight) {
    if (weight === null || weight === undefined) return '0.00';
    return Number(weight).toFixed(2);
  },

  // 切换标签
  changeTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab !== this.data.currentTab) {
      this.setData({
        currentTab: tab
      });
      this.loadOrders(true);
    }
  },

  // 跳转到订单详情
  goToOrderDetail: function (e) {
    const orderId = e.currentTarget.dataset.orderid;
    wx.navigateTo({
      url: '/pages/order/detail?orderId=' + orderId
    });
  },

  // 取消订单
  cancelOrder: function (e) {
    console.log(e.currentTarget.dataset)
    const orderId = e.currentTarget.dataset.id;
    const orderIndex = e.currentTarget.dataset.index;

    wx.showModal({
      title: '提示',
      content: '确定要取消该订单吗？',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });

          // 调用取消订单API
          wx.request({
            url: `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/${orderId}/cancel`,
            method: 'PUT',
            header: {
              'Authorization': `Bearer ${wx.getStorageSync('token')}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            success: (res) => {
              wx.hideLoading();
              if (res.data.code === 200) {
                // 更新本地数据
                const orders = this.data.orders;
                orders[orderIndex].status = 0; // 0 = 已取消
                orders[orderIndex].statusText = '已取消';
                this.setData({
                  orders: orders
                });

                wx.showToast({
                  title: '订单已取消',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: res.data.message || '取消失败',
                  icon: 'none'
                });
              }
            },
            fail: (error) => {
              wx.hideLoading();
              console.error('取消订单失败:', error);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });

    // 阻止事件冒泡
    return false;
  },

  // 确认价格
  confirmPrice: function (e) {
    const orderId = e.currentTarget.dataset.orderid;
    const orderIndex = e.currentTarget.dataset.index;

    // 检查倒计时是否已过期
    const orders = this.data.orders;
    const order = orders.find(o => o.orderId === orderId);
    if (order && order.countdownExpired) {
      wx.showToast({
        title: '确认时间已过期',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认价格',
      content: '确认接受此检测价格吗？确认后将进入结算流程。',
      success: res => {
        if (res.confirm) {
          wx.showLoading({
            title: '处理中...'
          });

          // 调用确认价格API
          wx.request({
            url: `${app.globalData.apiConfig.baseUrl}/api/gold-recycle/orders/${orderId}/status`,
            method: 'PUT',
            header: {
              'Authorization': `Bearer ${wx.getStorageSync('token')}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: {
              status: 5 // 状态5表示已确认
            },
            success: (res) => {
              wx.hideLoading();
              if (res.data.code === 200) {
                // 更新本地数据
                const orders = this.data.orders;
                orders[orderIndex].status = 5; // 5 = 已确认
                orders[orderIndex].statusText = '已确认';
                this.setData({
                  orders: orders
                });

                wx.showToast({
                  title: '价格确认成功',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: res.data.message || '确认失败',
                  icon: 'none'
                });
              }
            },
            fail: (error) => {
              wx.hideLoading();
              console.error('确认价格失败:', error);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 复制订单号
  copyOrderId: function (e) {
    const orderId = e.currentTarget.dataset.orderid;
    if (orderId) {
      wx.setClipboardData({
        data: orderId,
        success: function () {
          wx.showToast({
            title: '订单号已复制',
            icon: 'success',
            duration: 1500
          });
        },
        fail: function () {
          wx.showToast({
            title: '复制失败',
            icon: 'none',
            duration: 1500
          });
        }
      });
    }
    // 阻止事件冒泡，避免触发订单详情跳转
    e.stopPropagation();
  },

  // 联系客服
  contactService: function (e) {
    // 获取订单号
    let orderId = '';
    if (e && e.currentTarget && e.currentTarget.dataset) {
      orderId = e.currentTarget.dataset.orderid;
    }

    // 如果没有从事件中获取到订单号，尝试从当前点击的订单卡片获取
    if (!orderId && e && e.target) {
      // 向上查找包含订单号的父元素
      let target = e.target;
      while (target && !orderId) {
        if (target.dataset && target.dataset.orderid) {
          orderId = target.dataset.orderid;
          break;
        }
        target = target.parentNode;
      }
    }

    // 跳转到客服页面
    const url = orderId
      ? `/pages/customerService/index?orderId=${orderId}`
      : '/pages/customerService/index';

    wx.navigateTo({
      url: url,
      fail: (error) => {
        console.error('跳转客服页面失败:', error);
        wx.showToast({
          title: '跳转失败，请重试',
          icon: 'none'
        });
      }
    });

    // 阻止事件冒泡
    return false;
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return [year, month, day].map(this.formatNumber).join('-') + ' ' +
           [hour, minute].map(this.formatNumber).join(':');
  },

  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  },

  // 图片加载成功
  onImageLoad: function(e) {
    const orderId = e.currentTarget.dataset.orderid;
    console.log('图片加载成功:', orderId);
  },

  // 图片加载失败
  onImageError: function(e) {
    const orderId = e.currentTarget.dataset.orderid;
    console.error('图片加载失败:', orderId, e.detail);

    // 可以在这里设置默认图片或者隐藏图片
    wx.showToast({
      title: '图片加载失败',
      icon: 'none',
      duration: 1000
    });
  }
})
