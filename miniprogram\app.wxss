/**app.wxss**/
@font-face {
  font-family: 'iconfont';
  src: url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAMMAAsAAAAABwQAAAK+AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDBgqBaIFyATYCJAMMCwgABCAFhG0HNRvZBcgekiSIQAEFCgBQPPy3H+2+mZn/EBFUQ1W9nY5EoVAkFArJEA/Uhr0n2Xm5/3nV0iBLUJlnVYpQCQs5PvdO/wElMr9ALuccfLwOMA4o0L01G0iABOQNYxdwCfdhgE5paqLDkxdvMJAxuwXiVaeLG4xZLMqQTWgV6pZDi/iIRpt+pC8AH/z348+IGBqNptLZe/Ik1YPCr8LPx0i1/q+uKrsI+vMZYFtUWAMyxFVt6Aw5wVaOnG7FYKwKBr8K//9HtlcrqP3LIzSEqjO7G1hEJvwqzFME+FUlJRroRJcG4B5gBzDFoaHRJ7Pf4uDT3vj8/Oj4/PQYPz2FD/fh3W18cwNfX6M2V5dXFxcX52cX52en5/j0FB8f4f09vr3Fd7d4cCU/u9rfVty7tYr7t1bxwPYqHtq1ioZ3raJhXato+JZ1NLJnHY3uWUeje9fR2P51NL5/HU0cXEeThzbQ1OENlBw+3D+Jh4bw4ADu78O9Pbi7C3d24PY23NqCm5twYwOur8O1NVhdhZUVWF6GpSVYXITFRSQWFvDcPMzNwewszMzA9DRMTcHkJExMwPg4jI3ByAiMjMDwMAwNweAgDAzA8+fQr9/zZ9CnD/T2Qk8PdHdDVxd0dkJHB7S3Q1sbtLZCSws0N0NTEzQ2QkMD1NdDXR3U1kJNDVRXQ1UVVFZCRSVER0JkJIRDICwMQkMhJARCQiA4GIKCIDCwKyAA3b+ydUH3r9sA4Pt3MgDwzXQFAPDbdBsA/DYlnwb8KvycrSL4/9iVY/xvlgIAn59fIQo5iSqrLGNWRTMrs+LVVT0AnehEJ6BRO7QFtPvSdtLxRZNoaWsQGi1JqLQsoTLsRdRot6HSsgGdlj0c7TYkzo52KAuYsgSEXl+h0e0HKr1+UBn2N2qM+odKbxDoXAwRJ7YrC5hJIQ5qDLmGlmUTRjJKpFJnqO8jylOPSXlFlfAGueQc9phnxJQYYoYr5BwcI89jjEvkHJnGKBqmKYhT6DSGw1QyPEe9Xm/Qc9ZNGHVTAOOgDIZYg1RLZoQiRhKS0jkf9X0R5aOeIlV9ohTCN5CTHAc7TA8TWqmWMJbXpTicBjkeDMPFRZRzyChjKBRYTIJwFHQyDAepSgzvRXp6egZ5XM7qK+qXt0EnY7ZFihwlarToLSexJJGcUCgAAAA=') format('woff2');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 32rpx;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
page {
  --primary-color: #4A1010;
  --secondary-color: #6B1919;
  --primary-gradient: linear-gradient(135deg, #6B1919, #4A1010);
  --text-color: #333333;
  --light-text-color: #666666;
  --background-color: #F8F0F0;
  --border-color: #D9C1C1;

  background-color: var(--background-color);
  font-size: 28rpx;
  color: var(--text-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

.container {
  padding: 20rpx;
  box-sizing: border-box;
}

.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-color);
}

.text-secondary {
  color: var(--secondary-color);
}

.text-light {
  color: var(--light-text-color);
}

.bg-primary {
  background-color: var(--primary-color);
}

.bg-white {
  background-color: #ffffff;
}

.card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(74, 16, 16, 0.1);
}

.btn-primary {
  background: var(--primary-gradient);
  color: #ffffff;
  border: none;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 10rpx rgba(74, 16, 16, 0.3);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 1rpx solid var(--primary-color);
  border-radius: 45rpx;
}

.divider {
  height: 1rpx;
  background-color: var(--border-color);
  width: 100%;
  margin: 20rpx 0;
}

.price {
  font-weight: bold;
  color: var(--primary-color);
}

.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.tag-hot {
  background-color: var(--primary-color);
  color: #ffffff;
}

.tag-new {
  background-color: var(--secondary-color);
  color: #ffffff;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin: 30rpx 0 20rpx;
  position: relative;
}

.empty-tip {
  text-align: center;
  padding: 60rpx 0;
  color: var(--light-text-color);
}

.loading {
  text-align: center;
  padding: 20rpx 0;
  color: var(--light-text-color);
}
