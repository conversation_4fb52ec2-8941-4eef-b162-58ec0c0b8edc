// pages/shop/shop.js
const app = getApp();

Page({
  data: {
    categories: [
      { id: 'all', name: '全部' },
      { id: 'ring', name: '戒指' },
      { id: 'necklace', name: '项链' },
      { id: 'bracelet', name: '手镯' },
      { id: 'earring', name: '耳饰' },
      { id: 'pendant', name: '吊坠' },
      { id: 'goldbar', name: '金条' },
      { id: 'bangle', name: '手链' },
      { id: 'ancient', name: '古法金' },
      { id: 'pure', name: '黄金' },
      { id: '3d', name: '3D黄金' },
      { id: '5d', name: '5D黄金' },
      { id: '5g', name: '5G黄金' }
    ],
    currentCategory: 'all',
    sortOptions: [
      { id: 'default', name: '默认排序' },
      { id: 'priceAsc', name: '价格从低到高' },
      { id: 'priceDesc', name: '价格从高到低' },
      { id: 'salesDesc', name: '销量优先' },
      { id: 'newest', name: '最新上架' }
    ],
    currentSort: 'default',
    products: [],
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    keyword: '',
    showFilter: false,
    // 投资金条数据
    investmentProducts: [
      {
        id: 'product1',
        image: '/images/products/gold1.png',
        price: '3.30',
        unit: '件'
      },
      {
        id: 'product2',
        image: '/images/products/gold2.png',
        price: '2.00',
        unit: '克'
      },
      {
        id: 'product3',
        image: '/images/products/gold3.png',
        price: '2.00',
        unit: '克'
      },
      {
        id: 'product4',
        image: '/images/products/gold4.png',
        price: '5.99',
        unit: '克'
      }
    ],
    // 热销商品数据
    hotProducts: [
      {
        id: 'hot1',
        image: '/images/products/hot1.png',
        price: '17.00',
        tag: '热销榜'
      },
      {
        id: 'hot2',
        image: '/images/products/hot2.png',
        price: '10.50',
        tag: '超级爆款'
      },
      {
        id: 'hot3',
        image: '/images/products/hot3.png',
        price: '8.50',
        tag: '严选现货'
      },
      {
        id: 'hot4',
        image: '/images/products/hot4.png',
        price: '18.00',
        tag: '新品推荐'
      }
    ],
    // 精选商品数据
    featuredProducts: [
      {
        id: 'featured1',
        image: '/images/products/featured1.png',
        name: '足金999 5G 凯蒂猫 项链',
        priceRange: '4.70-5.70克',
        tag: '5G',
        productTag: 'TOP 畅销款 全网热卖',
        price: '18.00',
        minOrder: '5件起订'
      },
      {
        id: 'featured2',
        image: '/images/products/featured2.png',
        name: '足金999.9 通用 中国金条',
        priceRange: '5.00-1,000.00克',
        tag: '金条',
        productTag: 'TOP 金条热销款 全网爆款',
        price: '2.00',
        minOrder: '1件起订'
      }
    ]
  },

  onLoad: function (options) {
    // 如果有分类参数，设置当前分类
    if (options.category) {
      this.setData({
        currentCategory: options.category
      });
    }

    this.loadProducts(true);
    this.loadInvestmentProducts();
  },

  onShow: function () {
    // 页面显示时检查是否需要刷新数据
    if (this.data.needRefresh) {
      this.loadProducts(true);
      this.setData({
        needRefresh: false
      });
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadProducts(true).then(() => {
      wx.stopPullDownRefresh();
    });
  },

  onReachBottom: function () {
    // 上拉加载更多
    if (this.data.hasMore && !this.data.loading) {
      this.loadProducts(false);
    }
  },

  onShareAppMessage: function () {
    // 分享小程序
    return {
      title: '黄金饰品商城，精选优质黄金饰品',
      path: '/pages/shop/shop?inviter=' + (app.globalData.openid || ''),
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 加载投资金条数据
  loadInvestmentProducts: function() {
    // 这里可以从云数据库加载真实数据
    // 目前使用的是静态数据
    console.log('加载投资金条数据');
  },

  // 加载商品列表
  loadProducts: function (refresh) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        products: []
      });
    }

    if (!this.data.hasMore || this.data.loading) {
      return Promise.resolve();
    }

    this.setData({ loading: true });

    return new Promise((resolve) => {
      const db = wx.cloud.database();
      const _ = db.command;

      // 构建查询条件
      let query = {
        status: 'on'
      };

      // 分类筛选
      if (this.data.currentCategory !== 'all') {
        query.category = this.data.currentCategory;
      }

      // 关键词搜索
      if (this.data.keyword) {
        query.name = db.RegExp({
          regexp: this.data.keyword,
          options: 'i'
        });
      }

      // 构建排序条件
      let orderField = 'createTime';
      let orderDirection = 'desc';

      switch (this.data.currentSort) {
        case 'priceAsc':
          orderField = 'price';
          orderDirection = 'asc';
          break;
        case 'priceDesc':
          orderField = 'price';
          orderDirection = 'desc';
          break;
        case 'salesDesc':
          orderField = 'sales';
          orderDirection = 'desc';
          break;
        case 'newest':
          orderField = 'createTime';
          orderDirection = 'desc';
          break;
      }

      // 查询数据
      db.collection('products')
        .where(query)
        .orderBy(orderField, orderDirection)
        .skip((this.data.page - 1) * this.data.pageSize)
        .limit(this.data.pageSize)
        .get()
        .then(res => {
          const newProducts = res.data;

          this.setData({
            products: this.data.products.concat(newProducts),
            loading: false,
            hasMore: newProducts.length === this.data.pageSize,
            page: this.data.page + 1
          });

          resolve();
        })
        .catch(err => {
          console.error('获取商品列表失败', err);
          this.setData({
            loading: false
          });
          resolve();
        });
    });
  },

  // 切换分类
  changeCategory: function (e) {
    const category = e.currentTarget.dataset.category;
    if (category !== this.data.currentCategory) {
      this.setData({
        currentCategory: category
      });
      this.loadProducts(true);
    }
  },

  // 切换排序
  changeSort: function (e) {
    const sort = e.currentTarget.dataset.sort;
    if (sort !== this.data.currentSort) {
      this.setData({
        currentSort: sort,
        showFilter: false
      });
      this.loadProducts(true);
    }
  },

  // 搜索商品
  searchProducts: function (e) {
    let keyword = '';
    if (e.detail && e.detail.value !== undefined) {
      keyword = e.detail.value;
    } else {
      keyword = this.data.keyword;
    }

    this.setData({
      keyword: keyword
    });
    this.loadProducts(true);
  },

  // 清除搜索关键词
  clearKeyword: function () {
    this.setData({
      keyword: ''
    });
    this.loadProducts(true);
  },

  // 显示筛选面板
  showFilterPanel: function () {
    this.setData({
      showFilter: true
    });
  },

  // 隐藏筛选面板
  hideFilterPanel: function () {
    this.setData({
      showFilter: false
    });
  },

  // 跳转到商品详情
  goToProductDetail: function (e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product/detail?id=' + productId
    });
  },

  // 显示所有分类
  showAllCategories: function() {
    wx.showToast({
      title: '查看更多分类',
      icon: 'none'
    });
  },

  // 显示更多热销商品
  showMoreHotProducts: function() {
    wx.showToast({
      title: '查看更多热销商品',
      icon: 'none'
    });
  },

  // 显示更多精选商品
  showMoreFeaturedProducts: function() {
    wx.showToast({
      title: '查看更多精选商品',
      icon: 'none'
    });
  }
})
