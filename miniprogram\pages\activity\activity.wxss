/* pages/activity/activity.wxss */
page {
  --primary-color: #8B4513; /* 棕色 */
  --secondary-color: #A0522D; /* 深棕色 */
  --light-color: #D2B48C; /* 浅棕色 */
  --accent-color: #CD853F; /* 秘鲁棕色 */
  --red-color: #E53935; /* 红色 */
  --background-color: #F5F5F5; /* 背景色 */
  --text-color: #333333; /* 文本色 */
  --light-text: #666666; /* 浅色文本 */
  --border-color: #EEEEEE; /* 边框色 */
  background-color: var(--background-color);
}

.container {
  padding-bottom: 30rpx;
}

/* 顶部轮播图 */
.banner-swiper {
  width: 100%;
  height: 300rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 通用样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
}

.section-title-wrap {
  display: flex;
  align-items: center;
}

.section-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: var(--text-color);
}

.section-more {
  font-size: 24rpx;
  color: #999;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 秒杀活动区域 */
.seckill-section {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.countdown-container {
  display: flex;
  align-items: center;
}

.countdown-label {
  font-size: 24rpx;
  color: var(--light-text);
  margin-right: 10rpx;
}

.countdown-timer {
  display: flex;
  align-items: center;
}

.countdown-block {
  width: 40rpx;
  height: 40rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6rpx;
}

.countdown-colon {
  margin: 0 6rpx;
  color: var(--primary-color);
  font-weight: bold;
}

.seckill-scroll {
  width: 100%;
  white-space: nowrap;
  padding: 0 20rpx 20rpx;
}

.seckill-products {
  display: flex;
}

.seckill-product {
  width: 300rpx;
  margin-right: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  display: inline-block;
}

.seckill-product:last-child {
  margin-right: 0;
}

.product-image {
  width: 100%;
  height: 300rpx;
  background-color: #f5f5f5;
}

.product-info {
  padding: 16rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 10rpx;
}

.product-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.product-price {
  color: var(--red-color);
  margin-right: 10rpx;
}

.price-symbol {
  font-size: 24rpx;
}

.price-value {
  font-size: 36rpx;
  font-weight: bold;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-progress {
  margin-bottom: 10rpx;
}

.progress-bar {
  height: 10rpx;
  background-color: #f5f5f5;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 6rpx;
}

.progress-inner {
  height: 100%;
  background: linear-gradient(to right, #FF9800, #FF5722);
  border-radius: 5rpx;
}

.progress-text {
  font-size: 22rpx;
  color: #FF5722;
  text-align: right;
}

.offline-tag {
  display: inline-block;
  font-size: 20rpx;
  color: #fff;
  background-color: var(--primary-color);
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-bottom: 10rpx;
}

.seckill-btn {
  width: 100%;
  height: 60rpx;
  line-height: 60rpx;
  background: linear-gradient(to right, #FF4D4F, #D4380D);
  color: #fff;
  font-size: 26rpx;
  border-radius: 30rpx;
  margin: 0;
  padding: 0;
}

.seckill-btn.sold-out {
  background: #ccc;
  color: #fff;
}

/* 活动专区 */
.activity-section {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.activity-grid {
  padding: 0 20rpx 20rpx;
}

.activity-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.activity-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.activity-name {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 10rpx;
}

.activity-desc {
  font-size: 24rpx;
  color: var(--light-text);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 黄金资讯 */
.news-section {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.news-list {
  padding: 0 20rpx 20rpx;
}

.news-item {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.news-item:last-child {
  border-bottom: none;
}

.news-content {
  flex: 1;
  margin-right: 20rpx;
}

.news-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 10rpx;
}

.news-meta {
  font-size: 24rpx;
  color: #999;
}

.news-source {
  margin-right: 20rpx;
}

.news-image {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.success-text {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 10rpx;
}

.success-tips {
  font-size: 28rpx;
  color: var(--light-text);
  margin-bottom: 20rpx;
  text-align: center;
}

.order-number {
  font-size: 28rpx;
  color: var(--text-color);
  background-color: #f5f5f5;
  padding: 10rpx 20rpx;
  border-radius: 6rpx;
  margin-bottom: 20rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f5f5f5;
}

.modal-btn {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  font-size: 30rpx;
  margin: 0;
  border-radius: 0;
}

.modal-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.modal-btn.confirm {
  background-color: var(--primary-color);
  color: #fff;
}