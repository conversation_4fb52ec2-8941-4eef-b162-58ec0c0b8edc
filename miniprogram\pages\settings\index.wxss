/* pages/settings/index.wxss */
page {
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.container {
  padding-bottom: 30rpx;
}

/* 设置列表 */
.settings-list {
  margin: 20rpx 0;
}

.settings-group {
  margin-bottom: 20rpx;
  background-color: #fff;
}

.settings-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.settings-item:last-child {
  border-bottom: none;
}

.item-title {
  font-size: 30rpx;
  color: #333;
}

.item-value {
  font-size: 28rpx;
  color: #999;
}

.item-arrow {
  width: 30rpx;
  height: 30rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jaGV2cm9uLXJpZ2h0Ij48cG9seWxpbmUgcG9pbnRzPSI5IDE4IDE1IDEyIDkgNiI+PC9wb2x5bGluZT48L3N2Zz4=');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 退出登录按钮 */
.logout-btn {
  margin: 40rpx 30rpx;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  background-color: #fff;
  color: #FF4D4F;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: 1rpx solid #FF4D4F;
}

/* 版本信息 */
.version-info {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  margin-top: 40rpx;
}
