import { ElMessage } from 'element-plus'
import { getUserRelationFee, getUserDetails, getPlatformFeeRate, depositToAccount } from '@/api/rebate'

/**
 * 处理返点分佣
 * @param {string} account - 当前用户账号
 * @param {number} weight - 订单重量
 * @param {string} goldType - 贵金属类型 (jewelry/bar/broken/other)
 * @returns {Promise<boolean>} 返回处理结果
 */
export async function processRebate(account, weight, goldType = 'jewelry') {
  try {
    console.log(`开始处理返点分佣 - 用户: ${account}, 重量: ${weight}, 贵金属类型: ${goldType}`)

    // 参数验证
    if (!account || !weight || weight <= 0) {
      console.warn('返点分佣参数无效:', { account, weight, goldType })
      return false
    }

    // 贵金属类型转换为category
    const goldTypeToCategory = {
      'jewelry': 0,  // 黄金
      'bar': 1,      // 铂金
      'broken': 2    // 钯金
      // 其他类型不参与返点，不设置映射
    }
    const category = goldTypeToCategory[goldType]

    // 如果是其他类型（不参与返点），直接返回
    if (category === undefined) {
      console.log(`贵金属类型 ${goldType} 不参与返点，跳过返点处理`)
      return true
    }

    // 1. 获取用户关系费率信息
    let relationFeeData
    try {
      const relationFeeResponse = await getUserRelationFee(account)
      relationFeeData = relationFeeResponse.data
      console.log('用户关系费率信息:', relationFeeData)
    } catch (error) {
      console.error('获取用户关系费率信息失败:', error)
      ElMessage.warning('获取用户关系信息失败，跳过返点处理')
      return false
    }

    // 检查是否有上级用户
    if (!relationFeeData.parentId) {
      console.log('用户无上级，无需返点')
      return true
    }

    // 2. 获取当前用户详情
    let currentUserData
    try {
      const userResponse = await getUserDetails(account)
      currentUserData = userResponse.data
      console.log('当前用户详情:', currentUserData)
    } catch (error) {
      console.error('获取当前用户详情失败:', error)
      ElMessage.error('获取用户详情失败')
      return false
    }

    // 3. 获取所有平台费率信息
    let allFeeRates = []
    try {
      const feeResponse = await getPlatformFeeRate()
      console.log('所有手续费率API响应:', feeResponse)

      if (feeResponse && Array.isArray(feeResponse.data)) {
        allFeeRates = feeResponse.data
      } else if (Array.isArray(feeResponse)) {
        allFeeRates = feeResponse
      } else {
        console.warn('未找到有效的手续费率数据:', feeResponse)
        allFeeRates = []
      }

      console.log('所有费率数据:', allFeeRates)
    } catch (error) {
      console.error('获取手续费率失败:', error)
      ElMessage.error('获取手续费率失败')
      return false
    }

    // 4. 根据用户等级和贵金属类型获取当前用户费率
    const currentUserFeeData = allFeeRates.find(fee =>
      fee.level === currentUserData.userLevel && fee.category === category
    )
    const currentUserFeeRate = currentUserFeeData ? currentUserFeeData.feeRate : 0
    console.log('当前用户手续费率:', currentUserFeeRate, `(等级:${currentUserData.userLevel}, 贵金属:${goldType}, category:${category})`)

    // 5. 处理上级用户返点
    if (relationFeeData.parentId && relationFeeData.parentFeeRate !== null && relationFeeData.parentFeeRate !== undefined) {
      // 获取上级用户的对应费率
      const parentUserData = await getUserDetails(relationFeeData.parentId)
      const parentFeeData = allFeeRates.find(fee =>
        fee.level === parentUserData.userLevel && fee.category === category
      )
      const parentFeeRate = parentFeeData ? parentFeeData.feeRate : relationFeeData.parentFeeRate

      const parentRebateAmount = (currentUserFeeRate - parentFeeRate) * weight
      console.log(`上级用户返点计算: (${currentUserFeeRate} - ${parentFeeRate}) * ${weight} = ${parentRebateAmount}`)
      console.log(`上级用户等级: ${parentUserData.userLevel}, 对应${goldType}(category:${category})费率: ${parentFeeRate}`)

      if (parentRebateAmount > 0) {
        try {
          await depositToAccount(relationFeeData.parentId, {
            amount: parentRebateAmount,
            transactionId: Date.now().toString(),
            remark: `${account}${goldType}返点`
          })
          console.log(`上级用户 ${relationFeeData.parentId} 返点成功: ${parentRebateAmount}`)
        } catch (error) {
          console.error('上级用户返点失败:', error)
          ElMessage.error(`上级用户返点失败: ${error.message}`)
        }
      } else {
        console.log('上级用户返点金额为负数或零，跳过返点')
      }
    }

    // 6. 处理上上级用户返点
    if (relationFeeData.grandParentId && relationFeeData.grandParentFeeRate !== null && relationFeeData.grandParentFeeRate !== undefined) {
      // 获取上上级用户的对应费率
      const grandParentUserData = await getUserDetails(relationFeeData.grandParentId)
      const grandParentFeeData = allFeeRates.find(fee =>
        fee.level === grandParentUserData.userLevel && fee.category === category
      )
      const grandParentFeeRate = grandParentFeeData ? grandParentFeeData.feeRate : relationFeeData.grandParentFeeRate

      // 重新获取上级用户费率用于计算
      const parentUserData = await getUserDetails(relationFeeData.parentId)
      const parentFeeData = allFeeRates.find(fee =>
        fee.level === parentUserData.userLevel && fee.category === category
      )
      const parentFeeRate = parentFeeData ? parentFeeData.feeRate : relationFeeData.parentFeeRate

      const grandParentRebateAmount = (parentFeeRate - grandParentFeeRate) * weight
      console.log(`上上级用户返点计算: (${parentFeeRate} - ${grandParentFeeRate}) * ${weight} = ${grandParentRebateAmount}`)
      console.log(`上上级用户等级: ${grandParentUserData.userLevel}, 对应${goldType}(category:${category})费率: ${grandParentFeeRate}`)

      if (grandParentRebateAmount > 0) {
        try {
          await depositToAccount(relationFeeData.grandParentId, {
            amount: grandParentRebateAmount,
            transactionId: Date.now().toString() + '_gp', // 添加后缀避免重复
            remark: `${account}${goldType}返点`
          })
          console.log(`上上级用户 ${relationFeeData.grandParentId} 返点成功: ${grandParentRebateAmount}`)
        } catch (error) {
          console.error('上上级用户返点失败:', error)
          ElMessage.error(`上上级用户返点失败: ${error.message}`)
        }
      } else {
        console.log('上上级用户返点金额为负数或零，跳过返点')
      }
    }

    console.log('返点分佣处理完成')
    return true

  } catch (error) {
    console.error('返点分佣处理异常:', error)
    ElMessage.error(`返点处理失败: ${error.message}`)
    return false
  }
}

/**
 * 带加载状态的返点处理函数
 * @param {string} account - 当前用户账号
 * @param {number} weight - 订单重量
 * @param {string} goldType - 贵金属类型 (jewelry/bar/broken/other)
 * @param {Function} setLoading - 设置加载状态的函数
 * @returns {Promise<boolean>} 返回处理结果
 */
export async function processRebateWithLoading(account, weight, goldType = 'jewelry', setLoading) {
  if (setLoading) setLoading(true)

  try {
    const result = await processRebate(account, weight, goldType)
    return result
  } finally {
    if (setLoading) setLoading(false)
  }
}
