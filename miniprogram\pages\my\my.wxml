<!--pages/my/my.wxml-->
<wxs module="utils">
  function getUserLevelText(userLevel) {
    switch(userLevel) {
      case 1:
        return 'VIP';
      case 2:
        return 'SVIP';
      case 3:
        return '企业客户';
      default:
        return 'Lv.' + (userLevel || 0);
    }
  }

  module.exports = {
    getUserLevelText: getUserLevelText
  };
</wxs>

<view class="container">

  <!-- 用户信息 -->
  <view class="user-info-section">
    <!-- 已登录状态 -->
    <view class="user-info" wx:if="{{isLogin}}">
      <image class="user-avatar" src="{{userInfo.avatar || '/images/default-avatar.png'}}"></image>
      <view class="user-detail">
        <view class="user-nickname">{{userInfo.username || userInfo.account}}</view>
        <view class="user-level">
          <text class="level-tag">{{utils.getUserLevelText(userInfo.userLevel || 0)}}</text>
          <text class="user-desc">{{userInfo.userLevel > 0 ? '尊享会员权益' : '完成认证升级会员等级'}}</text>
        </view>
        <view class="user-level-link" bindtap="goToMemberCenter">等级规则 ></view>
      </view>
      <view class="settings-icon" bindtap="goToSettings">
        <text class="icon-text">⚙️</text>
      </view>
    </view>

    <!-- 未登录状态 -->
    <view class="user-info not-login" wx:else bindtap="goToLogin">
      <image class="user-avatar" src="/images/default-avatar.png"></image>
      <view class="user-detail">
        <view class="user-nickname">立即登录</view>
        <view class="user-desc">登录后享受更多权益</view>
      </view>
    </view>
  </view>

  <!-- 我的钱包 -->
  <view class="wallet-section" bindtap="goToWallet">
    <view class="wallet-header">
      <view class="wallet-title">我的钱包</view>
      <view class="wallet-more">查看明细 ></view>
    </view>
    <view class="wallet-content">
      <view class="wallet-item">
        <view class="wallet-amount">{{accountInfo.balance || '0.00'}}</view>
        <view class="wallet-label">定金余额 (元)</view>
      </view>

      <view class="wallet-item">
        <view class="wallet-amount">{{accountInfo.totalRebate || '0.00'}}</view>
        <view class="wallet-label">积分</view>
      </view>
    </view>
  </view>

  <!-- 实名认证提醒横幅 -->
  <view class="auth-banner" wx:if="{{isLogin && userInfo.userLevel === 0}}" bindtap="goToVerification">
    <view class="auth-banner-content">
      <view class="auth-banner-icon">!</view>
      <view class="auth-banner-text">完成实名认证，提升会员等级，享受更多权益</view>
      <view class="auth-banner-arrow">></view>
    </view>
  </view>

  <!-- 我的订单 -->
  <view class="order-section">
    <view class="section-header">
      <view class="section-title">我的订单</view>
    </view>
    <view class="order-icons">
      <view class="order-icon-item" bindtap="goToOrderList" data-type="sell">
        <image class="order-icon" src="/images/icons/sell-order.png"></image>
        <view class="order-icon-text">回收订单</view>
      </view>
      <view class="order-icon-item" bindtap="goToOrderList" data-type="buy">
        <image class="order-icon" src="/images/icons/buy-order.png"></image>
        <view class="order-icon-text">锁价订单</view>
      </view>
      <view class="order-icon-item" bindtap="goToOrderList" data-type="jewelry">
        <image class="order-icon" src="/images/icons/jewelry-order.png"></image>
        <view class="order-icon-text">商城订单</view>
      </view>
    </view>
  </view>

  <!-- 功能区 -->
  <view class="function-grid">
    <!-- <view class="function-item" bindtap="goToDeliveryRecord">
      <image class="function-icon" src="/images/savegold-record.png"></image>
      <text class="function-text">存金记录</text>
    </view> -->

    <view class="function-item" bindtap="goToAddressManage">
      <image class="function-icon" src="/images/address-record.png"></image>
      <text class="function-text">地址管理</text>
    </view>

    <view class="function-item" bindtap="goToCustomerManage">
      <image class="function-icon" src="/images/custom-record.png"></image>
      <text class="function-text">客户管理</text>
    </view>

    <view class="function-item" bindtap="goToInvite">
      <image class="function-icon" src="/images/invite-record.png"></image>
      <text class="function-text">邀请有礼</text>
    </view>
  </view>

  <!-- 底部协议 -->
  <view class="footer-links">
    <navigator url="/pages/privacy/index" class="footer-link">《隐私政策》</navigator>
    <navigator url="/pages/service/index" class="footer-link">《用户服务协议》</navigator>
  </view>

</view>
