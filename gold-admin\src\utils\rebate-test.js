// 返点功能测试文件
// 用于验证贵金属类型映射关系

/**
 * 测试贵金属类型映射
 */
export function testGoldTypeMapping() {
  const goldTypeToCategory = {
    'jewelry': 0,  // 黄金
    'bar': 1,      // 铂金
    'broken': 2    // 钯金
    // 其他类型不参与返点，不设置映射
  }

  console.log('=== 贵金属类型映射测试 ===')
  console.log('jewelry (黄金) → category:', goldTypeToCategory['jewelry'])
  console.log('bar (铂金) → category:', goldTypeToCategory['bar'])
  console.log('broken (钯金) → category:', goldTypeToCategory['broken'])
  console.log('other (其他) → category:', goldTypeToCategory['other'], '(undefined = 不参与返点)')
  
  // 验证映射关系
  const testCases = [
    { goldType: 'jewelry', expectedCategory: 0, description: '黄金' },
    { goldType: 'bar', expectedCategory: 1, description: '铂金' },
    { goldType: 'broken', expectedCategory: 2, description: '钯金' },
    { goldType: 'other', expectedCategory: undefined, description: '其他(不参与返点)' }
  ]

  testCases.forEach(testCase => {
    const actualCategory = goldTypeToCategory[testCase.goldType]
    const isCorrect = actualCategory === testCase.expectedCategory
    console.log(`✓ ${testCase.description}: ${testCase.goldType} → ${actualCategory} ${isCorrect ? '✅' : '❌'}`)
  })

  console.log('=== 映射测试完成 ===')
}

/**
 * 模拟费率数据测试
 */
export function testFeeRateMatching() {
  // 模拟API返回的费率数据
  const mockFeeRates = [
    { level: 0, category: 0, feeRate: 0.05, levelName: '未实名用户', description: '黄金' },
    { level: 0, category: 1, feeRate: 0.04, levelName: '未实名用户', description: '铂金' },
    { level: 0, category: 2, feeRate: 0.03, levelName: '未实名用户', description: '钯金' },
    { level: 1, category: 0, feeRate: 0.04, levelName: 'VIP', description: '黄金' },
    { level: 1, category: 1, feeRate: 0.03, levelName: 'VIP', description: '铂金' },
    { level: 1, category: 2, feeRate: 0.025, levelName: 'VIP', description: '钯金' },
    { level: 2, category: 0, feeRate: 0.03, levelName: 'SVIP', description: '黄金' },
    { level: 2, category: 1, feeRate: 0.025, levelName: 'SVIP', description: '铂金' },
    { level: 2, category: 2, feeRate: 0.02, levelName: 'SVIP', description: '钯金' }
  ]

  console.log('=== 费率匹配测试 ===')
  
  // 测试不同用户等级和贵金属类型的费率匹配
  const testScenarios = [
    { userLevel: 0, goldType: 'jewelry', category: 0 },
    { userLevel: 1, goldType: 'bar', category: 1 },
    { userLevel: 2, goldType: 'broken', category: 2 },
    { userLevel: 1, goldType: 'other', category: undefined }
  ]

  testScenarios.forEach(scenario => {
    if (scenario.category !== undefined) {
      const feeData = mockFeeRates.find(fee => 
        fee.level === scenario.userLevel && fee.category === scenario.category
      )
      console.log(`用户等级${scenario.userLevel} + ${scenario.goldType} → 费率: ${feeData ? feeData.feeRate : '未找到'}`)
    } else {
      console.log(`用户等级${scenario.userLevel} + ${scenario.goldType} → 不参与返点`)
    }
  })

  console.log('=== 费率匹配测试完成 ===')
}

// 如果在浏览器环境中运行测试
if (typeof window !== 'undefined') {
  window.testRebateMapping = {
    testGoldTypeMapping,
    testFeeRateMatching
  }
}
