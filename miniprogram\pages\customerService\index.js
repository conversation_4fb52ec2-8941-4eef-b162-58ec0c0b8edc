// pages/customerService/index.js
Page({
  data: {
    orderId: '', // 订单号
    faqExpanded: [false, false, false] // FAQ展开状态
  },

  onLoad: function (options) {
    // 获取传递的订单号
    if (options.orderId) {
      this.setData({
        orderId: options.orderId
      });
    }
  },

  // 长按二维码
  onLongPressQR: function () {
    const qrCodeUrl = '/images/customer-service-qr.jpg'; // 获取二维码图片路径
    wx.previewImage({
      urls: [qrCodeUrl],
      current: qrCodeUrl,
      success: function (res) {
        console.log('预览图片成功', res);
      },
      fail: function (err) {
        console.error('预览图片失败', err);
        wx.showToast({
          title: '无法预览图片，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  // 图片加载失败
  onImageError: function () {
    wx.showToast({
      title: '客服二维码加载失败',
      icon: 'none'
    });
  },

  // 复制订单号
  copyOrderId: function () {
    if (!this.data.orderId) {
      wx.showToast({
        title: '暂无订单号',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: this.data.orderId,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 切换FAQ展开状态
  toggleFaq: function (e) {
    const index = e.currentTarget.dataset.index;
    const faqExpanded = this.data.faqExpanded;
    faqExpanded[index] = !faqExpanded[index];
    
    this.setData({
      faqExpanded: faqExpanded
    });
  }
});
