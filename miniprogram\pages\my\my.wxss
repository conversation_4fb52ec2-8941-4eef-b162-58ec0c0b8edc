/* pages/my/my.wxss */
page {
  background-color: #FFF7F7;
}

.container {
  padding-bottom: 30rpx;
  background-color: #FFF7F7;
}

/* 用户信息区域 */
.user-info-section {
  padding: 20rpx 10rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #eee;
}

.user-detail {
  margin-left: 20rpx;
  flex: 1;
}

.user-nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.user-level {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.level-tag {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(to right, #FF4D4F, #D4380D);
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  margin-right: 10rpx;
}

.user-desc {
  font-size: 24rpx;
  color: #666;
}

.user-level-link {
  font-size: 24rpx;
  color: #999;
}

.settings-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.not-login {
  padding: 20rpx 0;
}

.login-arrow {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-text {
  font-size: 30rpx;
  color: #999;
}

/* 钱包区域 */
.wallet-section {
  margin: 20rpx 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.wallet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.wallet-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.wallet-more {
  font-size: 26rpx;
  color: #999;
}

.wallet-content {
  display: flex;
  justify-content: space-between;
}

.wallet-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.wallet-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 10rpx;
  bottom: 10rpx;
  width: 1rpx;
  background-color: #eee;
}

.wallet-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF4D4F;
  margin-bottom: 10rpx;
}

.wallet-label {
  font-size: 24rpx;
  color: #666;
}

/* 订单区域 */
.order-section {
  margin: 20rpx 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  margin-top: -30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 26rpx;
  color: #999;
}

.order-icons {
  display: flex;
  justify-content: space-around;
}

.order-icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.order-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 16rpx;
}

.order-icon-text {
  font-size: 26rpx;
  color: #666;
}

/* 功能区 */
.function-grid {
  margin: 20rpx 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  flex-wrap: wrap;
}

.function-item {
  width: 33.33%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10rpx;
}

.function-icon {
  width: 55rpx;
  height: 55rpx;
  margin-bottom: 10rpx;
  margin-top: 10rpx;
}

.function-text {
  font-size: 26rpx;
  color: #666;
}

/* 底部协议 */
.footer-links {
  display: flex;
  justify-content: center;
  margin: 30rpx 0;
}

.footer-link {
  font-size: 24rpx;
  color: #999;
  margin: 0 10rpx;
}

/* 实名认证横幅 */
.auth-banner {
  margin: 20rpx 10rpx;
  background: linear-gradient(to right, #FFF2E8, #FFF7F7);
  border-radius: 16rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.auth-banner-content {
  display: flex;
  align-items: center;
}

.auth-banner-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #FF4D4F;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 16rpx;
}

.auth-banner-text {
  flex: 1;
  font-size: 26rpx;
  color: #FF4D4F;
}

.auth-banner-arrow {
  font-size: 30rpx;
  color: #FF4D4F;
  margin-left: 10rpx;
}
