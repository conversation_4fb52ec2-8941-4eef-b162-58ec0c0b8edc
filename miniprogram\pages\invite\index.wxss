/* pages/invite/index.wxss */
.container {
  padding-bottom: 30rpx;
}

/* 邀请卡片 */
.invite-card {
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 20rpx;
  color: #fff;
  box-shadow: 0 4rpx 20rpx rgba(212, 56, 13, 0.3);
}

.invite-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.invite-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.invite-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
}

.invite-code-container {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 12rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.invite-code-label {
  font-size: 28rpx;
  opacity: 0.9;
}

.invite-code-value {
  font-size: 32rpx;
  font-weight: bold;
  flex: 1;
  text-align: center;
  letter-spacing: 2rpx;
}

.invite-code-copy {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 26rpx;
}

.invite-actions {
  display: flex;
  justify-content: center;
}

.invite-action-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8rpx;
  padding: 15rpx 40rpx;
  font-size: 28rpx;
  color: #fff;
}

.share-btn {
  margin: 0;
  line-height: normal;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border-radius: 8rpx;
}

.share-btn::after {
  border: none;
}

/* 上级用户卡片 */
.superior-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.superior-header {
  margin-bottom: 20rpx;
}

.superior-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.superior-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: #FF4D4F;
  border-radius: 4rpx;
}

.superior-info {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.superior-avatar-container {
  margin-right: 20rpx;
}

.superior-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.superior-detail {
  flex: 1;
}

.superior-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.superior-account {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.superior-time {
  font-size: 24rpx;
  color: #999;
}

.bind-superior {
  padding: 20rpx 0;
}

.bind-tip {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  margin-bottom: 30rpx;
  line-height: 1.4;
}

.bind-form {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.bind-input-container {
  flex: 1;
}

.bind-input {
  width: 100%;
  height: 80rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.bind-btn {
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  color: #fff;
  border-radius: 8rpx;
  padding: 0 30rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  text-align: center;
  white-space: nowrap;
}

/* 统计卡片 */
.stats-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.stats-item {
  text-align: center;
  flex: 1;
}

.stats-divider {
  width: 2rpx;
  height: 80rpx;
  background-color: #f0f0f0;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #FF6B00;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 26rpx;
  color: #666;
}

/* 规则卡片 */
.rules-card {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.rules-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.rules-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background-color: var(--primary-color);
  border-radius: 4rpx;
}

.rules-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

.rule-item {
  margin-bottom: 10rpx;
}
