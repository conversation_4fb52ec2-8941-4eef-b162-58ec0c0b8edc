// pages/activity/activity.js
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {

    // 秒杀倒计时
    countdownHours: '00',
    countdownMinutes: '00',
    countdownSeconds: '00',
    endTime: null, // 秒杀结束时间

    // 秒杀商品数据
    seckillProducts: [
      {
        id: 's001',
        name: '足金999黄金貔貅吊坠',
        imageUrl: '/images/products/gold-pendant.jpg',
        originalPrice: '1299.00',
        seckillPrice: '999.00',
        stock: 50,
        sold: 32,
        soldPercent: 64, // 已售百分比
        isSoldOut: false
      },
      {
        id: 's002',
        name: '足金999黄金手链',
        imageUrl: '/images/products/gold-bracelet.jpg',
        originalPrice: '2199.00',
        seckillPrice: '1799.00',
        stock: 30,
        sold: 28,
        soldPercent: 93,
        isSoldOut: false
      },
      {
        id: 's003',
        name: '足金999黄金项链',
        imageUrl: '/images/products/gold-necklace.jpg',
        originalPrice: '3299.00',
        seckillPrice: '2799.00',
        stock: 20,
        sold: 20,
        soldPercent: 100,
        isSoldOut: true
      },
      {
        id: 's004',
        name: '足金999黄金戒指',
        imageUrl: '/images/products/gold-ring.jpg',
        originalPrice: '1599.00',
        seckillPrice: '1299.00',
        stock: 40,
        sold: 15,
        soldPercent: 38,
        isSoldOut: false
      }
    ],

    // 活动专区数据
    activityItems: [
      {
        id: 'a001',
        name: '黄金回收特惠',
        description: '旧金换新金，额外赠送工费',
        imageUrl: '/images/activities/recycle.jpg',
        linkUrl: '/pages/goldRecycle/index'
      },
      {
        id: 'a002',
        name: '新品首发',
        description: '新款黄金饰品，限时9折优惠',
        imageUrl: '/images/activities/new-arrival.jpg',
        linkUrl: '/pages/shop/shop'
      },
      {
        id: 'a003',
        name: '会员专享',
        description: '会员专享特权，积分双倍',
        imageUrl: '/images/activities/member.jpg',
        linkUrl: '/pages/my/my'
      }
    ],

    // 黄金资讯数据
    newsItems: [
      {
        id: 'n001',
        title: '国际金价创新高，投资黄金正当时',
        source: '黄金资讯',
        publishTime: '2024-05-28',
        imageUrl: '/images/news/gold-price.jpg'
      },
      {
        id: 'n002',
        title: '黄金饰品保养小技巧，让你的黄金永葆光彩',
        source: '黄金之家',
        publishTime: '2024-05-26',
        imageUrl: '/images/news/gold-care.jpg'
      },
      {
        id: 'n003',
        title: '2024年黄金投资趋势分析',
        source: '财经日报',
        publishTime: '2024-05-24',
        imageUrl: '/images/news/gold-investment.jpg'
      }
    ],

    // 秒杀成功弹窗
    showSuccessModal: false,
    currentOrderId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置秒杀结束时间（当前时间 + 2小时）
    const endTime = new Date();
    endTime.setHours(endTime.getHours() + 2);

    this.setData({
      endTime: endTime.getTime()
    });

    // 开始倒计时
    this.startCountdown();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 如果倒计时已经停止，重新开始
    if (!this.countdownTimer) {
      this.startCountdown();
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
    // 清除倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清除倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 模拟刷新数据
    setTimeout(() => {
      // 更新秒杀商品数据
      const seckillProducts = this.data.seckillProducts.map(item => {
        // 随机增加已售数量
        const soldIncrement = Math.floor(Math.random() * 3);
        let sold = item.sold + soldIncrement;

        // 确保不超过库存
        if (sold > item.stock) {
          sold = item.stock;
        }

        // 计算已售百分比
        const soldPercent = Math.floor((sold / item.stock) * 100);

        // 判断是否已售罄
        const isSoldOut = sold >= item.stock;

        return {
          ...item,
          sold,
          soldPercent,
          isSoldOut
        };
      });

      this.setData({
        seckillProducts
      });

      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 开始倒计时
   */
  startCountdown: function () {
    // 清除之前的定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }

    // 更新倒计时
    this.updateCountdown();

    // 设置定时器，每秒更新一次
    this.countdownTimer = setInterval(() => {
      this.updateCountdown();
    }, 1000);
  },

  /**
   * 更新倒计时
   */
  updateCountdown: function () {
    const now = new Date().getTime();
    const endTime = this.data.endTime;

    // 计算剩余时间（毫秒）
    let remainTime = endTime - now;

    if (remainTime <= 0) {
      // 倒计时结束
      clearInterval(this.countdownTimer);
      this.countdownTimer = null;

      this.setData({
        countdownHours: '00',
        countdownMinutes: '00',
        countdownSeconds: '00'
      });

      // 更新所有商品为已售罄
      const seckillProducts = this.data.seckillProducts.map(item => {
        return {
          ...item,
          isSoldOut: true
        };
      });

      this.setData({
        seckillProducts
      });

      return;
    }

    // 计算小时、分钟、秒
    const hours = Math.floor(remainTime / (1000 * 60 * 60));
    remainTime %= (1000 * 60 * 60);

    const minutes = Math.floor(remainTime / (1000 * 60));
    remainTime %= (1000 * 60);

    const seconds = Math.floor(remainTime / 1000);

    // 格式化时间
    const formatNumber = n => n < 10 ? '0' + n : n;

    this.setData({
      countdownHours: formatNumber(hours),
      countdownMinutes: formatNumber(minutes),
      countdownSeconds: formatNumber(seconds)
    });
  },

  /**
   * 处理秒杀抢购
   */
  handleSeckill: function (e) {
    // 阻止事件冒泡
    e.stopPropagation();

    // 获取商品ID
    const id = e.currentTarget.dataset.id;

    // 检查用户是否登录
    if (!app.globalData.userInfo || !app.globalData.userInfo.isLogin) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });

      // 跳转到登录页面
      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);

      return;
    }

    // 显示加载提示
    wx.showLoading({
      title: '抢购中...',
      mask: true
    });

    // 模拟抢购请求
    setTimeout(() => {
      wx.hideLoading();

      // 查找商品
      const product = this.data.seckillProducts.find(item => item.id === id);

      if (!product) {
        wx.showToast({
          title: '商品不存在',
          icon: 'none'
        });
        return;
      }

      if (product.isSoldOut) {
        wx.showToast({
          title: '商品已抢光',
          icon: 'none'
        });
        return;
      }

      // 生成订单号
      const orderId = 'SK' + new Date().getTime() + Math.floor(Math.random() * 1000);

      // 更新商品数据
      const seckillProducts = this.data.seckillProducts.map(item => {
        if (item.id === id) {
          // 增加已售数量
          const sold = item.sold + 1;

          // 计算已售百分比
          const soldPercent = Math.floor((sold / item.stock) * 100);

          // 判断是否已售罄
          const isSoldOut = sold >= item.stock;

          return {
            ...item,
            sold,
            soldPercent,
            isSoldOut
          };
        }
        return item;
      });

      this.setData({
        seckillProducts,
        currentOrderId: orderId,
        showSuccessModal: true
      });
    }, 1500);
  },

  /**
   * 关闭成功弹窗
   */
  closeSuccessModal: function () {
    this.setData({
      showSuccessModal: false
    });
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail: function () {
    // 关闭弹窗
    this.setData({
      showSuccessModal: false
    });

    // 跳转到订单详情页面
    wx.navigateTo({
      url: '/pages/order/detail?id=' + this.data.currentOrderId
    });
  },

  /**
   * 跳转到商品详情
   */
  goToProductDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product/detail?id=' + id
    });
  },

  /**
   * 跳转到轮播图链接
   */
  navigateToBanner: function (e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: url
    });
  },

  /**
   * 跳转到活动页面
   */
  navigateToActivity: function (e) {
    const url = e.currentTarget.dataset.url;
    wx.navigateTo({
      url: url
    });
  },

  /**
   * 查看更多新闻
   */
  viewMoreNews: function () {
    wx.showToast({
      title: '更多资讯功能开发中',
      icon: 'none'
    });
  },

  /**
   * 查看新闻详情
   */
  viewNewsDetail: function (e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: '资讯详情功能开发中',
      icon: 'none'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    return {
      title: '限时秒杀，黄金饰品低至7折',
      path: '/pages/activity/activity',
      imageUrl: '/images/share/seckill-share.jpg'
    };
  }
})