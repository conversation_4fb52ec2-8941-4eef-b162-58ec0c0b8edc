/* pages/shop/shop.wxss */
page {
  --primary-color: #8B4513; /* 棕色 */
  --secondary-color: #A0522D; /* 深棕色 */
  --light-color: #D2B48C; /* 浅棕色 */
  --accent-color: #CD853F; /* 秘鲁棕色 */
  --red-color: #E53935; /* 红色 */
  --background-color: #F5F5F5; /* 背景色 */
  --text-color: #333333; /* 文本色 */
  --light-text: #666666; /* 浅色文本 */
  --border-color: #EEEEEE; /* 边框色 */
  background-color: var(--background-color);
}

.container {
  padding-bottom: 30rpx;
  background-color: #fff;
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-input-wrap {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.search-button {
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  background-color: #FF4D4F;
  color: #fff;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
}

.cart-button {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 20rpx;
}

.cart-icon {
  width: 50rpx;
  height: 50rpx;
}

/* 促销横幅 */
.promo-banner {
  margin: 0 20rpx 20rpx;
  background: linear-gradient(135deg, #8B0000, #A52A2A);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  overflow: hidden;
}

.promo-text {
  display: flex;
  flex-direction: column;
}

.promo-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.promo-subtitle {
  font-size: 28rpx;
}

.promo-highlight {
  font-size: 36rpx;
  font-weight: bold;
  color: #FFD700;
}

.promo-image {
  width: 160rpx;
  height: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.gold-bar-image {
  width: 100%;
  height: 100%;
}

/* 分类网格 */
.category-grid {
  margin: 0 20rpx 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.category-row {
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
}

.category-row:last-child {
  border-bottom: none;
}

.category-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  border-right: 1rpx solid #f5f5f5;
}

.category-item:last-child {
  border-right: none;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
}

.find-more {
  background-color: #FFD700;
}

.find-more-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.find-more-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 10rpx;
}

.find-more-arrow {
  font-size: 24rpx;
  color: #fff;
}

/* 小分类图标 */
.mini-categories {
  margin: 0 20rpx 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx 0;
}

.mini-categories-scroll {
  white-space: nowrap;
}

.mini-category-item {
  display: inline-block;
  width: 120rpx;
  text-align: center;
  margin: 0 20rpx;
}

.mini-category-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.mini-category-name {
  font-size: 24rpx;
  color: #333;
}

/* 热销商品 */
.hot-products-section {
  margin: 0 20rpx 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title-wrap {
  display: flex;
  align-items: center;
}

.hot-icon, .featured-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 10rpx;
}

.section-more {
  font-size: 24rpx;
  color: #999;
}

.hot-products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 15rpx;
}

.hot-product-item {
  position: relative;
  border-radius: 12rpx;
  overflow: hidden;
  height: 200rpx;
}

.hot-product-tag {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  background-color: rgba(255, 77, 79, 0.8);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  z-index: 10;
}

.hot-product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hot-product-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}

.hot-product-price {
  color: #fff;
  font-size: 26rpx;
  font-weight: bold;
}

/* 精选商品 */
.featured-products-section {
  margin: 0 20rpx 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
}

.featured-products-list {
  display: flex;
  flex-direction: column;
}

.featured-product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.featured-product-item:last-child {
  border-bottom: none;
}

.featured-product-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.featured-product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.featured-product-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.featured-product-price-range {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.price-range {
  font-size: 24rpx;
  color: #666;
  margin-right: 10rpx;
}

.product-tag {
  display: inline-block;
  background-color: #f5f5f5;
  color: #FF4D4F;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 4rpx;
}

.featured-product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.featured-product-tag {
  font-size: 22rpx;
  color: #FF4D4F;
  background-color: #FFF0F0;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
}

.featured-product-price {
  font-size: 24rpx;
  color: #FF4D4F;
  font-weight: bold;
}

.featured-product-sales {
  font-size: 22rpx;
  color: #999;
  text-align: right;
}

/* 商品列表 */
.products-container {
  padding: 20rpx;
}

.products-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-card {
  width: 48%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-info {
  padding: 16rpx;
}

.product-name {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.product-meta {
  display: flex;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.product-weight {
  margin-right: 10rpx;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  color: #FF4D4F;
  font-size: 32rpx;
  font-weight: bold;
}

.product-sales {
  font-size: 22rpx;
  color: #999;
}

.product-tags {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  display: flex;
}

.product-tag {
  font-size: 20rpx;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  color: #fff;
  margin-right: 10rpx;
}

.product-tag.hot {
  background-color: #FF4D4F;
}

.product-tag.new {
  background-color: #D4380D;
}

/* 加载状态 */
.loading-container {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
  font-size: 26rpx;
}

.empty {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

/* 筛选面板 */
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
}

.filter-panel.show {
  visibility: visible;
}

.filter-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.filter-panel.show .filter-mask {
  opacity: 1;
}

.filter-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.filter-panel.show .filter-content {
  transform: translateY(0);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
}

.filter-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.sort-options {
  padding: 20rpx 30rpx;
}

.sort-option {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.sort-option.active {
  color: var(--primary-color);
}
