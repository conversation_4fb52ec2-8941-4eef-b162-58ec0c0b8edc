<!--pages/wallet/index.wxml-->
<view class="container">
  <!-- 钱包卡片 -->
  <view class="wallet-card">
    <view class="wallet-card-header">
      <view class="wallet-card-title">账户余额 (元)</view>
    </view>
    <view class="wallet-card-balance">{{accountInfo.balance}}</view>
    <view class="wallet-card-actions">
      <view class="wallet-action-btn withdraw-btn" bindtap="goToWithdraw">提现</view>
      <view class="wallet-action-btn recharge-btn" bindtap="goToRecharge">充值</view>
    </view>
  </view>

  <!-- 资产明细 -->
  <view class="assets-section">
    <view class="assets-header">
      <view class="assets-title">我的资产</view>
    </view>
    <view class="assets-list">
      <view class="asset-item" bindtap="goToPoints">
        <view class="asset-icon points-icon">积</view>
        <view class="asset-info">
          <view class="asset-name">积分</view>
          <view class="asset-value">{{accountInfo.totalRebate}}分</view>
        </view>
        <view class="asset-arrow"></view>
      </view>
    </view>
  </view>

  <!-- 交易记录 -->
  <view class="transaction-section">
    <view class="transaction-header">
      <view class="transaction-title">交易记录</view>
      <view class="transaction-filter" bindtap="showFilterOptions">
        <text>{{currentFilter}}</text>
        <text class="filter-arrow">▼</text>
      </view>
    </view>

    <!-- 定金交易记录 -->
    <view class="transaction-list" wx:if="{{balanceTransactions.length > 0}}">
      <view class="transaction-item" wx:for="{{balanceTransactions}}" wx:key="id" bindtap="goToTransactionDetail" data-id="{{item.id}}">
        <view class="transaction-icon {{item.type === 'income' ? 'income-icon' : 'expense-icon'}}">
          {{item.type === 'income' ? '收' : '支'}}
        </view>
        <view class="transaction-info">
          <view class="transaction-title">{{item.title}}</view>
          <view class="transaction-time">{{item.createTime}}</view>
        </view>
        <view class="transaction-amount {{item.type === 'income' ? 'income-amount' : 'expense-amount'}}">
          {{item.type === 'income' ? '+' : '-'}}{{item.amount}}
        </view>
      </view>
    </view>

    <!-- 积分交易记录 -->
    <view class="transaction-list" wx:if="{{pointsTransactions.length > 0}}">
      <view class="transaction-item" wx:for="{{pointsTransactions}}" wx:key="id" bindtap="goToTransactionDetail" data-id="{{item.id}}">
        <view class="transaction-icon {{item.type === 'income' ? 'income-icon' : 'expense-icon'}}">
          {{item.type === 'income' ? '收' : '支'}}
        </view>
        <view class="transaction-info">
          <view class="transaction-title">{{item.title}}</view>
          <view class="transaction-time">{{item.createTime}}</view>
        </view>
        <view class="transaction-amount {{item.type === 'income' ? 'income-amount' : 'expense-amount'}}">
          {{item.type === 'income' ? '+' : '-'}}{{item.amount}}
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 空状态 -->
    <view class="empty-transactions" wx:if="{{!loading && balanceTransactions.length === 0 && pointsTransactions.length === 0}}">
      <view class="empty-icon">📝</view>
      <view class="empty-text">暂无交易记录</view>
    </view>
  </view>
</view>
