// pages/wallet/index.js
const app = getApp();

Page({
  data: {
    accountInfo: {
      balance: '0.00',
      totalRebate: '0.00'
    },
    currentFilter: '全部',
    allTransactions: [], // 所有交易记录
    balanceTransactions: [], // 定金交易记录
    pointsTransactions: [], // 积分交易记录
    pageNum: 1,
    pageSize: 10,
    hasMore: true,
    loading: false,
    total: 0
  },

  onLoad() {
    this.getAccountInfo();
    this.getTransactionRecords();
  },

  onShow() {
    this.getAccountInfo();
  },

  // 获取账户信息
  getAccountInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) return;

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/accounts/account/${userInfo.account}`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        if (res.data.code === 200) {
          const accountData = res.data.data;
          this.setData({
            accountInfo: {
              balance: accountData.balance.toFixed(2),
              totalRebate: accountData.totalRebate.toFixed(2)
            }
          });
        }
      }
    });
  },

  // 获取交易记录
  getTransactionRecords() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) {
      console.log('用户信息或账户ID不存在');
      return;
    }

    // 设置加载状态
    this.setData({
      loading: true
    });

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/api/account-transactions/list`,
      method: 'GET',
      data: {
        accountId: userInfo.account,
        current: this.data.pageNum,
        size: this.data.pageSize
      },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json'
      },
      success: (res) => {
        console.log('交易记录API响应:', res);
        this.setData({
          loading: false
        });

        if (res.data.code === 200 && res.data.data) {
          const responseData = res.data.data;
          const records = responseData.records || [];

          // 处理交易记录数据
          const processedTransactions = records.map(item => this.processTransactionItem(item));

          // 根据当前页数决定是追加还是替换数据
          const allTransactions = this.data.pageNum === 1 ?
            processedTransactions : [...this.data.allTransactions, ...processedTransactions];

          this.setData({
            allTransactions,
            total: responseData.total || 0,
            hasMore: responseData.current < responseData.pages
          });

          // 更新筛选后的交易记录显示
          this.updateFilteredTransactions();
        } else {
          wx.showToast({
            title: res.data.message || '获取交易记录失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('获取交易记录失败:', error);
        this.setData({
          loading: false
        });
        wx.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        });
      }
    });
  },

  // 处理单条交易记录数据
  processTransactionItem(item) {
    // 根据type字段确定交易类型和分类
    let transactionType = 'balance'; // 默认为定金交易
    let typeText = '未知交易';
    let isIncome = false;

    switch (item.type) {
      case 1:
        typeText = '账户充值';
        transactionType = 'balance';
        isIncome = true;
        break;
      case 2:
        typeText = '账户提现';
        transactionType = 'balance';
        isIncome = false;
        break;
      case 3:
        typeText = '返点增加';
        transactionType = 'points';
        isIncome = true;
        break;
      case 4:
        typeText = '返点减少';
        transactionType = 'points';
        isIncome = false;
        break;
      case 5:
        typeText = '手续费';
        transactionType = 'balance';
        isIncome = false;
        break;
      default:
        // 根据金额正负判断收支
        isIncome = item.amount > 0;
        typeText = isIncome ? '收入' : '支出';
        // 根据字段判断是定金还是积分交易
        if (item.balance !== undefined && item.balance !== null) {
          transactionType = 'balance';
        } else if (item.totalRebate !== undefined && item.totalRebate !== null) {
          transactionType = 'points';
        }
        break;
    }

    return {
      id: item.id,
      accountId: item.accountId,
      userId: item.userId,
      type: isIncome ? 'income' : 'expense',
      transactionType: transactionType,
      amount: Math.abs(item.amount || 0).toFixed(2),
      balance: (item.balance || 0).toFixed(2),
      totalRebate: (item.totalRebate || 0).toFixed(2),
      status: item.status,
      title: item.remark || typeText,
      remark: item.remark,
      createTime: this.formatTime(item.createTime),
      updateTime: this.formatTime(item.updateTime),
      rawData: item // 保留原始数据以备后用
    };
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';

    try {
      const date = new Date(timeStr);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

      const timeFormat = `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;

      if (itemDate.getTime() === today.getTime()) {
        return `今天 ${timeFormat}`;
      } else if (itemDate.getTime() === yesterday.getTime()) {
        return `昨天 ${timeFormat}`;
      } else {
        return `${date.getMonth() + 1}-${date.getDate()} ${timeFormat}`;
      }
    } catch (e) {
      console.error('时间格式化错误:', e);
      return timeStr;
    }
  },

  // 显示筛选选项
  showFilterOptions() {
    wx.showActionSheet({
      itemList: ['全部', '定金', '积分'],
      success: (res) => {
        const filters = ['全部', '定金', '积分'];
        const selectedFilter = filters[res.tapIndex];

        this.setData({
          currentFilter: selectedFilter
        });

        // 重新筛选和分类交易记录
        this.updateFilteredTransactions();
      }
    });
  },

  // 更新筛选后的交易记录显示
  updateFilteredTransactions() {
    const {
      currentFilter,
      allTransactions
    } = this.data;

    // 根据筛选条件更新显示的交易记录
    switch (currentFilter) {
      case '定金':
        this.setData({
          balanceTransactions: allTransactions.filter(item => item.transactionType === 'balance'),
          pointsTransactions: []
        });
        break;
      case '积分':
        this.setData({
          balanceTransactions: [],
          pointsTransactions: allTransactions.filter(item => item.transactionType === 'points')
        });
        break;
      default: // '全部'
        this.setData({
          balanceTransactions: allTransactions.filter(item => item.transactionType === 'balance'),
          pointsTransactions: allTransactions.filter(item => item.transactionType === 'points')
        });
        break;
    }
  },

  // 跳转到交易详情
  goToTransactionDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/wallet/transaction-detail?id=${id}`
    });
  },

  // 跳转到提现页面
  goToWithdraw() {
    wx.navigateTo({
      url: '/pages/wallet/withdraw'
    });
  },

  // 跳转到充值页面
  goToRecharge() {
    wx.navigateTo({
      url: '/pages/wallet/recharge'
    });
  },

  // 跳转到积分页面
  goToPoints() {
    wx.navigateTo({
      url: '/pages/wallet/points'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({
      pageNum: 1,
      allTransactions: [],
      balanceTransactions: [],
      pointsTransactions: [],
      hasMore: true
    });
    this.getAccountInfo();
    this.getTransactionRecords();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.setData({
        pageNum: this.data.pageNum + 1
      });
      this.getTransactionRecords();
    }
  },

  // 筛选交易记录
  filterTransactions() {
    const {
      currentFilter,
      allTransactions
    } = this.data;
    let filteredTransactions = [];

    switch (currentFilter) {
      case '定金':
        filteredTransactions = allTransactions.filter(item => item.transactionType === 'balance');
        break;
      case '积分':
        filteredTransactions = allTransactions.filter(item => item.transactionType === 'points');
        break;
      default:
        filteredTransactions = allTransactions;
        break;
    }

    return filteredTransactions;
  }
})