<template>
  <div class="admin-container">
    <div class="filter-container">
      <el-button
        type="primary"
        icon="Plus"
        @click="handleAdd"
      >
        添加管理员
      </el-button>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      element-loading-text="加载中..."
      border
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="ID" width="80">
        <template #default="{ row, $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>

      <el-table-column label="用户名" align="center">
        <template #default="{ row }">
          <span>{{ row.username }}</span>
        </template>
      </el-table-column>

      <el-table-column label="真实姓名" align="center">
        <template #default="{ row }">
          <span>{{ row.realName || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="邮箱" align="center">
        <template #default="{ row }">
          <span>{{ row.email || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="手机号" align="center">
        <template #default="{ row }">
          <span>{{ row.phone || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="管理员类型" align="center">
        <template #default="{ row }">
          <el-tag :type="row.adminType === 1 ? 'danger' : 'primary'">
            {{ row.adminType === 1 ? '超级管理员' : '普通管理员' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <el-button
            v-if="row.adminType !== 1"
            size="small"
            type="danger"
            @click="handleStatusChange(row)"
          >
            禁用
          </el-button>
          <span v-else class="text-muted">超级管理员</span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加管理员对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="添加管理员"
      width="500px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="form.username"
            placeholder="请输入用户名"
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="form.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="真实姓名" prop="realName">
          <el-input
            v-model="form.realName"
            placeholder="请输入真实姓名"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="form.email"
            placeholder="请输入邮箱"
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入手机号"
          />
        </el-form-item>

        <el-form-item label="管理员类型" prop="adminType">
          <el-select v-model="form.adminType" placeholder="请选择管理员类型">
            <el-option label="普通管理员" :value="0" />
            <el-option label="超级管理员" :value="1" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="submitLoading"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getAdminList, createAdmin, updateAdminStatus } from '@/api/admin'

// 数据
const list = ref([])
const listLoading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const formRef = ref()

// 表单数据
const form = ref({
  username: '',
  password: '',
  realName: '',
  email: '',
  phone: '',
  adminType: 0
})

// 表单验证规则
const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  adminType: [
    { required: true, message: '请选择管理员类型', trigger: 'change' }
  ]
}

// 获取管理员列表
async function getList() {
  listLoading.value = true
  try {
    const response = await getAdminList()
    if (response.code === 200) {
      list.value = response.data || []
    } else {
      ElMessage.error(response.message || '获取管理员列表失败')
    }
  } catch (error) {
    console.error('获取管理员列表失败:', error)
    ElMessage.error('获取管理员列表失败')
  } finally {
    listLoading.value = false
  }
}

// 添加管理员
function handleAdd() {
  dialogVisible.value = true
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    const response = await createAdmin(form.value)
    
    if (response.code === 200) {
      ElMessage.success('管理员创建成功')
      dialogVisible.value = false
      getList() // 刷新列表
    } else {
      ElMessage.error(response.message || '创建管理员失败')
    }
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      console.error('创建管理员失败:', error)
      ElMessage.error('创建管理员失败')
    }
  } finally {
    submitLoading.value = false
  }
}

// 状态变更
async function handleStatusChange(row) {
  try {
    await ElMessageBox.confirm('确定要禁用该管理员吗？', '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await updateAdminStatus(row.username, 0) // 假设0为禁用状态
    
    if (response.code === 200) {
      ElMessage.success('操作成功')
      getList() // 刷新列表
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('状态更新失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.value = {
    username: '',
    password: '',
    realName: '',
    email: '',
    phone: '',
    adminType: 0
  }
}

onMounted(() => {
  getList()
})
</script>

<style scoped>
.admin-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.text-muted {
  color: #999;
}

.dialog-footer {
  text-align: right;
}
</style>
