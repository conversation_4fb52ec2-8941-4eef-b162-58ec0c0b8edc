// pages/order/success.js
const app = getApp();

Page({
  data: {
    orderId: '',
    amount: 0,
    payTime: ''
  },

  onLoad: function (options) {
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderInfo();
    }
    
    if (options.amount) {
      this.setData({
        amount: parseFloat(options.amount)
      });
    }
    
    // 设置当前时间为支付时间
    const now = new Date();
    this.setData({
      payTime: this.formatTime(now)
    });
  },

  onShareAppMessage: function () {
    return {
      title: '我刚刚在黄金回收销售小程序购买了商品，推荐给你',
      path: '/pages/index/index?inviter=' + (app.globalData.openid || ''),
      imageUrl: '/images/share-cover.jpg'
    };
  },

  // 加载订单信息
  loadOrderInfo: function () {
    const db = wx.cloud.database();
    
    db.collection('orders')
      .doc(this.data.orderId)
      .get()
      .then(res => {
        const order = res.data;
        
        this.setData({
          amount: order.totalAmount / 100,
          payTime: this.formatTime(order.payTime || new Date())
        });
      })
      .catch(err => {
        console.error('获取订单信息失败', err);
      });
  },

  // 查看订单
  viewOrder: function () {
    wx.navigateTo({
      url: '/pages/order/detail?id=' + this.data.orderId
    });
  },

  // 返回首页
  goHome: function () {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 继续购物
  continueShopping: function () {
    wx.switchTab({
      url: '/pages/shop/shop'
    });
  },

  // 格式化时间
  formatTime: function (date) {
    date = new Date(date);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();
    const second = date.getSeconds();
    
    return [year, month, day].map(this.formatNumber).join('-') + ' ' + 
           [hour, minute, second].map(this.formatNumber).join(':');
  },
  
  formatNumber: function (n) {
    n = n.toString();
    return n[1] ? n : '0' + n;
  }
})
