<template>
  <div class="app-container">
    <div class="price-header">
      <h2>价格管理</h2>
      <p>基于实时市场价格设置黄金和铂金的回收和销售价格</p>
      <div class="refresh-section">
        <span class="last-update" v-if="lastUpdateTime">
          最后更新: {{ formatTime(lastUpdateTime) }}
        </span>
      </div>
    </div>

    <el-row :gutter="20">
      <!-- 黄金价格设置 -->
      <el-col :span="12">
        <el-card class="price-card">
          <template #header>
            <div class="card-header">
              <span>黄金价格设置</span>
              <el-icon class="gold-icon"><Coin /></el-icon>
            </div>
          </template>

          <el-divider />

          <el-form
            ref="goldFormRef"
            :model="goldForm"
            :rules="priceRules"
            label-width="120px"
            label-position="left"
          >
            <el-form-item label="回收价格" prop="recyclePrice">
              <el-input-number
                v-model="goldForm.recyclePrice"
                :precision="2"
                :min="-9999"
                :max="9999"
                style="width: 100%"
                placeholder="请输入回收价格"
              />
              <span class="unit">元/克</span>
            </el-form-item>

            <el-form-item label="销售价格" prop="sellPrice">
              <el-input-number
                v-model="goldForm.sellPrice"
                :precision="2"
                :min="-9999"
                :max="9999"
                style="width: 100%"
                placeholder="请输入销售价格"
              />
              <span class="unit">元/克</span>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveGoldPrice" :loading="goldLoading">
                保存黄金价格
              </el-button>
              <el-button @click="resetGoldForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 铂金价格设置 -->
      <el-col :span="12">
        <el-card class="price-card">
          <template #header>
            <div class="card-header">
              <span>铂金价格设置</span>
              <el-icon class="platinum-icon"><Medal /></el-icon>
            </div>
          </template>

          <el-divider />

          <el-form
            ref="platinumFormRef"
            :model="platinumForm"
            :rules="priceRules"
            label-width="120px"
            label-position="left"
          >
            <el-form-item label="回收价格" prop="recyclePrice">
              <el-input-number
                v-model="platinumForm.recyclePrice"
                :precision="2"
                :min="-9999"
                :max="9999"
                style="width: 100%"
                placeholder="请输入回收价格"
              />
              <span class="unit">元/克</span>
            </el-form-item>

            <el-form-item label="销售价格" prop="sellPrice">
              <el-input-number
                v-model="platinumForm.sellPrice"
                :precision="2"
                :min="-9999"
                :max="9999"
                style="width: 100%"
                placeholder="请输入销售价格"
              />
              <span class="unit">元/克</span>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="savePlatinumPrice" :loading="platinumLoading">
                保存铂金价格
              </el-button>
              <el-button @click="resetPlatinumForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Coin, Medal, Refresh } from '@element-plus/icons-vue'
import { getPriceControls, updatePriceControls } from '@/api/price'
import { parseTime } from '@/utils'

const goldFormRef = ref()
const platinumFormRef = ref()
const goldLoading = ref(false)
const platinumLoading = ref(false)
const refreshLoading = ref(false)
const lastUpdateTime = ref(null)

// 市场价格数据
const goldMarketPrice = ref({
  buyPrice: null,
  sellPrice: null
})

const platinumMarketPrice = ref({
  buyPrice: null,
  sellPrice: null
})

// 价格控制表单
const goldForm = ref({
  recyclePrice: 0,
  sellPrice: 0
})

const platinumForm = ref({
  recyclePrice: 0,
  sellPrice: 0
})

const priceRules = {
  recyclePrice: [
    { required: true, message: '请输入回收价格', trigger: 'blur' },
  ],
  sellPrice: [
    { required: true, message: '请输入销售价格', trigger: 'blur' },
  ]
}

// 加载价格控制配置
async function loadPriceControls() {
  try {
    // 加载黄金价格控制
    const goldResponse = await getPriceControls(1)
    if (goldResponse.data) {
      goldForm.value = {
        recyclePrice: goldResponse.data.recyclePrice || 0,
        sellPrice: goldResponse.data.sellPrice || 0
      }
    }

    // 加载铂金价格控制
    const platinumResponse = await getPriceControls(2)
    if (platinumResponse.data) {
      platinumForm.value = {
        recyclePrice: platinumResponse.data.recyclePrice || 0,
        sellPrice: platinumResponse.data.sellPrice || 0
      }
    }
  } catch (error) {
    console.error('加载价格控制配置失败:', error)
    // 使用默认值
    goldForm.value = { recyclePrice: 470.00, sellPrice: 500.00 }
    platinumForm.value = { recyclePrice: 200.00, sellPrice: 230.00 }
  }
}

// 保存黄金价格
async function saveGoldPrice() {
  if (!goldFormRef.value) return

  try {
    await goldFormRef.value.validate()
    goldLoading.value = true

    await updatePriceControls(1, goldForm.value.recyclePrice, goldForm.value.sellPrice)
    ElMessage.success('黄金价格保存成功')
  } catch (error) {
    if (error !== false) {
      console.error('保存黄金价格失败:', error)
      ElMessage.error('保存失败，请稍后重试')
    }
  } finally {
    goldLoading.value = false
  }
}

// 保存铂金价格
async function savePlatinumPrice() {
  if (!platinumFormRef.value) return

  try {
    await platinumFormRef.value.validate()
    platinumLoading.value = true

    await updatePriceControls(2, platinumForm.value.recyclePrice, platinumForm.value.sellPrice)
    ElMessage.success('铂金价格保存成功')
  } catch (error) {
    if (error !== false) {
      console.error('保存铂金价格失败:', error)
      ElMessage.error('保存失败，请稍后重试')
    }
  } finally {
    platinumLoading.value = false
  }
}

// 重置表单
function resetGoldForm() {
  goldFormRef.value?.resetFields()
  loadPriceControls()
}

function resetPlatinumForm() {
  platinumFormRef.value?.resetFields()
  loadPriceControls()
}

// 格式化时间
function formatTime(time) {
  if (!time) return '--'
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}')
}

// 页面初始化
onMounted(() => {
  loadPriceControls()
})
</script>

<style scoped>
.price-header {
  margin-bottom: 20px;
}

.price-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.price-header p {
  margin: 0 0 16px 0;
  color: #909399;
}

.refresh-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.last-update {
  color: #909399;
  font-size: 14px;
}

.price-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.gold-icon {
  color: #f39c12;
  font-size: 20px;
}

.platinum-icon {
  color: #95a5a6;
  font-size: 20px;
}

.market-price-section {
  margin-bottom: 16px;
}

.market-price-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.market-prices {
  display: flex;
  gap: 24px;
}

.price-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  min-width: 120px;
}

.price-item .label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.price-item .price {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.unit {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}
</style>
