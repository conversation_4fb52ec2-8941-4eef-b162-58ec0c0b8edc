// pages/address/index.js
const app = getApp();

Page({
  data: {
    addressList: [], // 地址列表
    showForm: false, // 是否显示地址表单
    editingAddress: null, // 当前编辑的地址ID
    formData: {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      isDefault: false
    },
    region: ['', '', '']
  },

  onLoad: function (options) {
    // 如果是从其他页面选择地址，记录来源页面
    if (options.from) {
      this.setData({
        fromPage: options.from
      });
    }
  },

  onShow: function () {
    // 加载地址列表
    this.loadAddressList();
  },

  // 加载地址列表
  loadAddressList: function () {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) {
      this.setData({
        addressList: []
      });
      return;
    }

    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    const apiData = {
      account: userInfo.account
    };

    const postData = Object.keys(apiData).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(apiData[key])}`).join('&');

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/user/address/list`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: postData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200 && res.data.data) {
          // 将API返回的isDefault (integer) 转换为 boolean
          const addressList = res.data.data.map(item => ({
            ...item,
            isDefault: item.isDefault === 1
          }));
          this.setData({
            addressList: addressList
          });
        } else {
          wx.showToast({
            title: res.data.message || '地址列表加载失败',
            icon: 'none'
          });
          this.setData({
            addressList: []
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        this.setData({
          addressList: []
        });
      }
    });
  },

  // 显示地址表单
  showAddressForm: function () {
    this.setData({
      showForm: true,
      editingAddress: null,
      formData: {
        name: '',
        phone: '',
        province: '',
        city: '',
        district: '',
        detail: '',
        isDefault: false
      },
      region: ['', '', '']
    });
  },

  // 隐藏地址表单
  hideAddressForm: function () {
    this.setData({
      showForm: false
    });
  },

  // 编辑地址
  editAddress: function (e) {
    const id = e.currentTarget.dataset.id;

    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 准备API请求数据
    const apiData = {
      addressId: id
    };

    // 转换数据为 form-urlencoded 格式
    const postData = Object.keys(apiData).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(apiData[key])}`).join('&');

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/user/address/detail`,
      method: 'POST',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: postData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200 && res.data.data) {
          const address = res.data.data;
          this.setData({
            showForm: true,
            editingAddress: id,
            formData: {
              name: address.receiverName,
              phone: address.receiverPhone,
              province: address.province,
              city: address.city,
              district: address.district,
              detail: address.detailAddress,
              isDefault: address.isDefault === 1 // API返回integer，转换为boolean
            },
            region: [address.province, address.city, address.district]
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除地址
  deleteAddress: function (e) {
    const id = e.currentTarget.dataset.id;

    wx.showModal({
      title: '提示',
      content: '确定要删除该地址吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '删除中...',
            mask: true
          });

          // 准备API请求数据
          const apiData = {
            addressId: id
          };

          // 转换数据为 form-urlencoded 格式
          const postData = Object.keys(apiData).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(apiData[key])}`).join('&');

          wx.request({
            url: `${app.globalData.apiConfig.baseUrl}/user/address/delete`,
            method: 'DELETE',
            header: {
              'Authorization': `Bearer ${wx.getStorageSync('token')}`,
              'Content-Type': 'application/x-www-form-urlencoded'
            },
            data: postData,
            success: (res) => {
              wx.hideLoading();
              if (res.data.code === 200) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                // 删除成功后重新加载地址列表
                this.loadAddressList();
              } else {
                wx.showToast({
                  title: res.data.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: () => {
              wx.hideLoading();
              wx.showToast({
                title: '网络请求失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 设置默认地址
  setDefault: function (e) {
    const id = e.currentTarget.dataset.id;

    wx.showLoading({
      title: '设置中...',
      mask: true
    });

    // 准备API请求数据
    const apiData = {
      addressId: id
    };

    // 转换数据为 form-urlencoded 格式
    const postData = Object.keys(apiData).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(apiData[key])}`).join('&');

    wx.request({
      url: `${app.globalData.apiConfig.baseUrl}/user/address/default`,
      method: 'PUT',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: postData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200) {
          wx.showToast({
            title: '设置成功',
            icon: 'success'
          });
          // 设置成功后重新加载地址列表以更新显示
          this.loadAddressList();
        } else {
          wx.showToast({
            title: res.data.message || '设置失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },

  // 选择地区
  regionChange: function (e) {
    this.setData({
      region: e.detail.value,
      'formData.province': e.detail.value[0],
      'formData.city': e.detail.value[1],
      'formData.district': e.detail.value[2]
    });
  },

  // 保存地址
  saveAddress: function (e) {
    const formData = e.detail.value;
    const { region, editingAddress } = this.data;

    // 表单验证
    if (!formData.name) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      });
      return;
    }

    if (!formData.phone) {
      wx.showToast({
        title: '请输入手机号码',
        icon: 'none'
      });
      return;
    }

    if (!/^1\d{10}$/.test(formData.phone)) {
      wx.showToast({
        title: '手机号码格式不正确',
        icon: 'none'
      });
      return;
    }

    if (!region[0] || !region[1] || !region[2]) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      });
      return;
    }

    if (!formData.detail) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.account) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 准备API请求数据
    const apiData = {
      account: userInfo.account,
      city: region[1],
      detailAddress: formData.detail,
      district: region[2],
      province: region[0],
      receiverName: formData.name,
      receiverPhone: formData.phone,
      isDefault: formData.isDefault ? 1 : 0 // API文档isDefault是integer(int32)
    };

    let url = '';
    let method = '';

    if (editingAddress) { // 编辑现有地址，调用更新API
      url = `${app.globalData.apiConfig.baseUrl}/user/address/update`;
      method = 'PUT';
      apiData.addressId = editingAddress; // 编辑时需要地址ID
    } else { // 添加新地址，调用新增API
      url = `${app.globalData.apiConfig.baseUrl}/user/address/add`;
      method = 'POST';
    }

    // 转换数据为 form-urlencoded 格式
    const postData = Object.keys(apiData).map(key => `${encodeURIComponent(key)}=${encodeURIComponent(apiData[key])}`).join('&');

    wx.request({
      url: url,
      method: method,
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      data: postData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.code === 200) {
          wx.showToast({
            title: editingAddress ? '编辑成功' : '添加成功',
            icon: 'success'
          });
          this.hideAddressForm();
          this.loadAddressList(); // 成功后重新从后端加载列表
        } else {
          wx.showToast({
            title: res.data.message || (editingAddress ? '编辑失败' : '添加失败'),
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },

  // 选择地址并返回
  selectAddress: function (e) {
    const id = e.currentTarget.dataset.id;
    console.log('选择地址ID:', id);

    // 从当前地址列表中查找选中的地址
    const selectedAddress = this.data.addressList.find(item => item.addressId === id);
    console.log('找到的地址:', selectedAddress);

    if (this.data.fromPage && selectedAddress) {
      // 将选中的地址传递回来源页面
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2]; // 上一个页面

      if (prevPage) {
        console.log('传递地址到上一页:', selectedAddress);

        // 将地址对象直接设置到前一个页面的 data 中
        prevPage.setData({
          selectedAddress: selectedAddress
        });

        // 如果上一页有 setAddress 方法，调用它
        if (typeof prevPage.setAddress === 'function') {
          console.log('调用上一页的 setAddress 方法');
          prevPage.setAddress(selectedAddress);
        }

        wx.navigateBack({
          delta: 1
        });
      }
    }
  }
});
