import request from '@/utils/request'

/**
 * 获取价格配置
 */
export function getPriceConfig() {
  return request({
    url: '/admin/price/config',
    method: 'get'
  })
}

/**
 * 更新价格配置
 * @param {string} type - 价格类型 (gold/platinum)
 * @param {object} data - 价格数据
 */
export function updatePriceConfig(type, data) {
  return request({
    url: `/admin/price/config/${type}`,
    method: 'put',
    data
  })
}

/**
 * 获取价格变更历史
 * @param {object} params - 查询参数
 */
export function getPriceHistory(params) {
  return request({
    url: '/admin/price/history',
    method: 'get',
    params
  })
}



/**
 * 获取价格控制设置
 * @param {number} priceType - 价格类型 (1: 黄金, 2: 铂金)
 */
export function getPriceControls(priceType) {
  return request({
    url: `/api/price-controls/type/${priceType}`,
    method: 'get'
  })
}

/**
 * 更新价格控制设置
 * @param {number} priceType - 价格类型 (1: 黄金, 2: 铂金)
 * @param {number} recyclePrice - 回收价格
 * @param {number} sellPrice - 销售价格
 */
export function updatePriceControls(priceType, recyclePrice, sellPrice) {
  return request({
    url: `/api/price-controls/type/${priceType}`,
    method: 'put',
    params: {
      recyclePrice,
      sellPrice
    }
  })
}
