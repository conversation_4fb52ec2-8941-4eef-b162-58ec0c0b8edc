import request from '@/utils/request'

/**
 * 获取充值提现列表
 * @param {object} params - 查询参数
 */
export function getRechargeList(params) {
  return request({
    url: '/admin/recharge/list',
    method: 'get',
    params
  })
}

/**
 * 更新充值提现状态
 * @param {number} id - 记录ID
 * @param {object} data - 更新数据
 */
export function updateRechargeStatus(id, data) {
  return request({
    url: `/admin/recharge/${id}/status`,
    method: 'put',
    data
  })
}

/**
 * 导出充值提现记录
 * @param {object} params - 查询参数
 */
export function exportRecharge(params) {
  return request({
    url: '/admin/recharge/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取充值提现统计
 */
export function getRechargeStats() {
  return request({
    url: '/admin/recharge/stats',
    method: 'get'
  })
}
