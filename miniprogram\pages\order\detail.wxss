/* pages/order/detail.wxss */
.container {
  padding-bottom: 120rpx;
}

/* 加载中 */
.loading-container {
  padding: 100rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 订单状态卡片 */
.status-card {
  background: linear-gradient(135deg, #FF4D4F, #D4380D);
  padding: 40rpx 30rpx;
  color: #fff;
  position: relative;
}

.status-icon {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.status-icon.status-1 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jbG9jayI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjEyIDYgMTIgMTIgMTYgMTQiPjwvcG9seWxpbmU+PC9zdmc+');
}

.status-icon.status-2 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1zZWFyY2giPjxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiPjwvY2lyY2xlPjxwYXRoIGQ9Im0yMSAyMS00LjM1LTQuMzUiPjwvcGF0aD48L3N2Zz4=');
}

.status-icon.status-3 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jaGVjay1jaXJjbGUiPjxwYXRoIGQ9Ik0yMiAxMS4wOFYxMmExMCAxMCAwIDEgMS01LjkzLTkuMTQiPjwvcGF0aD48cG9seWxpbmUgcG9pbnRzPSIyMiA0IDEyIDE0LjAxIDkgMTEuMDEiPjwvcG9seWxpbmU+PC9zdmc+');
}

.status-icon.status-4 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jaGVjay1jaXJjbGUiPjxwYXRoIGQ9Ik0yMiAxMS4wOFYxMmExMCAxMCAwIDEgMS01LjkzLTkuMTQiPjwvcGF0aD48cG9seWxpbmUgcG9pbnRzPSIyMiA0IDEyIDE0LjAxIDkgMTEuMDEiPjwvcG9seWxpbmU+PC9zdmc+');
}

.status-icon.status-5 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci10aHVtYnMtdXAiPjxwYXRoIGQ9Im0xNCA5IDMtMyAzIDMiPjwvcGF0aD48cGF0aCBkPSJNMTQgOXYxM2E0IDQgMCAwIDEtNCA0SDZhNCA0IDAgMCAxLTQtNFY5YTQgNCAwIDAgMSA0LTRoNGE0IDQgMCAwIDEgNCA0WiI+PC9wYXRoPjwvc3ZnPg==');
}

.status-icon.status-6 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jaGVjay1jaXJjbGUtMiI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiPjwvY2lyY2xlPjxwYXRoIGQ9Im05IDEyIDIgMiA0LTQiPjwvcGF0aD48L3N2Zz4=');
}

.status-icon.status-0 {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci14LWNpcmNsZSI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiPjwvY2lyY2xlPjxsaW5lIHgxPSIxNSIgeTE9IjkiIHgyPSI5IiB5Mj0iMTUiPjwvbGluZT48bGluZSB4MT0iOSIgeTE9IjkiIHgyPSIxNSIgeTI9IjE1Ij48L2xpbmU+PC9zdmc+');
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.status-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

/* 卡片通用样式 */
.address-card,
.gold-card,
.price-card,
.order-info-card,
.logistics-card,
.inspection-card {
  background-color: #fff;
  margin-top: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}

.card-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 30rpx;
  background-color: var(--primary-color);
  border-radius: 3rpx;
}

/* 地址卡片 */
.address-content {
  padding: 10rpx 0;
}

.address-loading {
  text-align: center;
  padding: 20rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.receiver-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.receiver-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  font-size: 22rpx;
  color: #FF4D4F;
  background-color: rgba(255, 77, 79, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  border: 1rpx solid #FF4D4F;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.address-extra {
  margin-top: 10rpx;
}

.postcode {
  font-size: 24rpx;
  color: #999;
}

.address-fallback .receiver-info {
  margin-bottom: 10rpx;
}

/* 黄金信息卡片 */
.gold-content {
  display: flex;
  flex-direction: column;
}

.gold-image-section {
  margin-bottom: 20rpx;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  justify-content: center;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.gold-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  border: 2rpx solid #f0f0f0;
  object-fit: cover;
}

.image-index {
  position: absolute;
  bottom: 5rpx;
  right: 5rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
}

.image-tip {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
  text-align: center;
}

.no-image-section {
  margin-bottom: 20rpx;
  text-align: center;
}

.no-image-placeholder {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.no-image-text {
  font-size: 24rpx;
  color: #999;
}

.gold-details {
  flex: 1;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 10rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}

.detail-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  min-width: 120rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 价格信息卡片 */
.price-details {
  padding: 10rpx 0;
}

.price-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 10rpx 0;
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-label {
  font-size: 28rpx;
  color: #666;
}

.price-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.price-value.final-price {
  color: #FF4D4F;
  font-weight: bold;
  font-size: 32rpx;
}

/* 检测结果卡片 */
.inspection-details {
  padding: 10rpx 0;
}

.inspection-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 10rpx 0;
}

.inspection-item:last-child {
  margin-bottom: 0;
}

.inspection-label {
  font-size: 28rpx;
  color: #666;
}

.inspection-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.inspection-value.final-price {
  color: #FF4D4F;
  font-weight: bold;
  font-size: 32rpx;
}

.inspection-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 16rpx 0;
  font-size: 28rpx;
  text-align: center;
  border-radius: 8rpx;
  font-weight: 500;
}

.confirm-btn {
  background-color: #FF4D4F;
  color: #fff;
  border: 1rpx solid #FF4D4F;
  position: relative;
}

.confirm-btn.disabled {
  background: #CCCCCC;
  color: #999999;
  cursor: not-allowed;
  border: 1rpx solid #CCCCCC;
}

.reject-btn {
  background-color: #fff;
  color: #FF4D4F;
  border: 1rpx solid #FF4D4F;
}

/* 检测状态样式 */
.inspection-status {
  margin-top: 30rpx;
  text-align: center;
}

.status-badge {
  display: inline-block;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.status-badge.confirmed {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52c41a;
  border: 1rpx solid #52c41a;
}

.status-badge.completed {
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  border: 1rpx solid #1890ff;
}

/* 物流卡片 */
.logistics-content {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  position: relative;
  padding: 10rpx 0;
}

.logistics-company {
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

.logistics-number {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.copy-btn {
  display: inline-block;
  font-size: 24rpx;
  color: var(--primary-color);
  margin-left: 10rpx;
  padding: 2rpx 10rpx;
  border: 1rpx solid var(--primary-color);
  border-radius: 20rpx;
}

.logistics-status {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.logistics-arrow {
  width: 30rpx;
  height: 30rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jaGV2cm9uLXJpZ2h0Ij48cG9seWxpbmUgcG9pbnRzPSI5IDE4IDE1IDEyIDkgNiI+PC9wb2x5bGluZT48L3N2Zz4=');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 商品卡片 */
.products-list {
  margin-bottom: 30rpx;
}

.product-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  line-height: 1.4;
}

.product-price-count {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  color: #FF6B00;
}

.product-count {
  font-size: 26rpx;
  color: #999;
}

.products-total {
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.total-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.total-label {
  font-size: 28rpx;
  color: #666;
}

.total-value {
  font-size: 28rpx;
  color: #333;
}

.total-value.highlight {
  font-size: 32rpx;
  font-weight: bold;
  color: #FF6B00;
}

.total-divider {
  height: 1rpx;
  background-color: #f0f0f0;
  margin: 16rpx 0;
}

/* 订单信息卡片 */
.order-info-list {
  padding: 10rpx 0;
}

.order-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.order-info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.bottom-action {
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  border: 1rpx solid #ddd;
  color: #666;
  background-color: #fff;
  line-height: 1.5;
}

.bottom-action.primary {
  background-color: var(--primary-color);
  color: #fff;
  border: 1rpx solid var(--primary-color);
}

.bottom-action.cancel {
  border: 1rpx solid #FF5252;
  color: #FF5252;
}

.bottom-action.confirm {
  background-color: #FF4D4F;
  color: #fff;
  border: 1rpx solid #FF4D4F;
  position: relative;
}

.bottom-action.confirm.disabled {
  background: #CCCCCC;
  color: #999999;
  cursor: not-allowed;
  border: 1rpx solid #CCCCCC;
}

.countdown-text {
  font-size: 22rpx;
  margin-left: 10rpx;
  opacity: 0.9;
}

.bottom-action.reject {
  border: 1rpx solid #FF4D4F;
  color: #FF4D4F;
  background-color: #fff;
}

.contact-btn {
  padding: 16rpx 30rpx;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin-left: 20rpx;
  border: 1rpx solid #ddd;
  color: #666;
  background-color: #fff;
  line-height: 1.5;
}

.contact-btn::after {
  border: none;
}

/* 空状态 */
.empty-container {
  padding: 100rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.go-back {
  padding: 16rpx 40rpx;
  background-color: var(--primary-color);
  color: #fff;
  font-size: 28rpx;
  border-radius: 30rpx;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  visibility: hidden;
}

.modal.show {
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.modal.show .modal-mask {
  opacity: 1;
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s;
}

.modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  padding: 0 20rpx;
}

.modal-body {
  padding: 30rpx;
}

/* 支付面板 */
.pay-amount {
  font-size: 60rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
}

.pay-methods {
  margin-bottom: 40rpx;
}

.pay-method {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.pay-method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
}

.pay-method-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.pay-method-selected {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 1rpx solid var(--primary-color);
  position: relative;
}

.pay-method-selected::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: var(--primary-color);
}

.pay-button {
  background-color: var(--primary-color);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
}

/* 取消订单面板 */
.cancel-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.cancel-reasons {
  margin-bottom: 30rpx;
}

.cancel-reason-item {
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.cancel-reason-item.selected {
  color: var(--primary-color);
}

.cancel-reason-item.selected::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjRkZENzAwIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jaGVjayI+PHBvbHlsaW5lIHBvaW50cz0iMjAgNiA5IDE3IDQgMTIiPjwvcG9seWxpbmU+PC9zdmc+');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.cancel-other {
  margin-bottom: 30rpx;
}

.cancel-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  font-size: 28rpx;
}

.cancel-button {
  background-color: #FF5252;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 8rpx;
}

/* 物流详情面板 */
.logistics-modal {
  height: 70%;
  max-height: 1000rpx;
}

.logistics-detail-header {
  margin-bottom: 30rpx;
}

.logistics-traces {
  max-height: 800rpx;
  overflow-y: auto;
}

.logistics-trace-item {
  position: relative;
  padding-left: 40rpx;
  padding-bottom: 40rpx;
  border-left: 1rpx solid #ddd;
}

.logistics-trace-item::before {
  content: '';
  position: absolute;
  left: -10rpx;
  top: 6rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ddd;
}

.logistics-trace-item.active::before {
  background-color: var(--primary-color);
}

.trace-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.trace-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.logistics-trace-item.active .trace-time,
.logistics-trace-item.active .trace-content {
  color: var(--primary-color);
}

/* 物流信息卡片样式 */
.logistics-loading {
  text-align: center;
  padding: 20rpx 0;
}

.logistics-content {
  padding: 10rpx 0;
}

.logistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.logistics-company {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.logistics-status {
  font-size: 26rpx;
  color: #FF4D4F;
  background-color: rgba(255, 77, 79, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid #FF4D4F;
}

.logistics-number {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.logistics-time {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.logistics-contact {
  margin: 15rpx 0;
  padding: 15rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.contact-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.call-btn {
  color: #FF4D4F;
  font-size: 24rpx;
  padding: 4rpx 8rpx;
  border: 1rpx solid #FF4D4F;
  border-radius: 4rpx;
  background-color: rgba(255, 77, 79, 0.1);
}

.logistics-tracks {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.tracks-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: flex;
  align-items: center;
}

.tracks-count {
  font-size: 24rpx;
  font-weight: normal;
  color: #999;
  margin-left: 10rpx;
}

.track-list {
  position: relative;
}

.track-item {
  position: relative;
  padding-left: 40rpx;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-left: 2rpx solid #e0e0e0;
}

.track-item:last-child {
  border-left: none;
  margin-bottom: 0;
}

.track-item::before {
  content: '';
  position: absolute;
  left: -6rpx;
  top: 8rpx;
  width: 10rpx;
  height: 10rpx;
  background-color: #FF4D4F;
  border-radius: 50%;
}

.track-item:first-child::before {
  background-color: #52c41a;
}

.track-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.track-status {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  margin-bottom: 5rpx;
}

.track-location {
  font-size: 24rpx;
  color: #666;
}

.logistics-error {
  text-align: center;
  padding: 30rpx 0;
}

.error-text {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.retry-btn {
  color: #FF4D4F;
  font-size: 26rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid #FF4D4F;
  border-radius: 4rpx;
  background-color: rgba(255, 77, 79, 0.1);
  display: inline-block;
}

.logistics-empty {
  text-align: center;
  padding: 30rpx 0;
}

/* 快递员信息样式 */
.courier-info {
  margin: 15rpx 0;
  padding: 15rpx 0;
  border-top: 1rpx solid #f0f0f0;
  border-bottom: 1rpx solid #f0f0f0;
}

.courier-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  color: #666;
}

.courier-item:last-child {
  margin-bottom: 0;
}

/* 时间信息样式 */
.time-info {
  margin: 15rpx 0;
}

.time-item {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.time-item:last-child {
  margin-bottom: 0;
}

/* 检测结果详情样式 */
.inspection-loading {
  text-align: center;
  padding: 40rpx 0;
}

/* 检测结果项容器 */
.quotation-items-container {
  margin-bottom: 30rpx;
}

.quotation-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.quotation-item:last-child {
  margin-bottom: 0;
}

.item-type-tag {
  font-size: 24rpx;
  color: #FF4D4F;
  background-color: rgba(255, 77, 79, 0.1);
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  border: 1rpx solid #FF4D4F;
  display: inline-block;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.item-content {
  display: flex;
  gap: 20rpx;
}

.item-image-container {
  flex-shrink: 0;
  width: 200rpx;
}

.item-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.quotation-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  border: 2rpx solid #e9ecef;
  object-fit: cover;
}

.no-image-placeholder {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.no-image-text {
  font-size: 24rpx;
  color: #999;
}

.item-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 200rpx;
}

.data-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.data-row:last-child {
  margin-bottom: 0;
}

.data-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 12rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
  margin-right: 8rpx;
  min-height: 44rpx;
}

.data-item:last-child {
  margin-right: 0;
}

.data-label {
  font-size: 20rpx;
  color: #666;
}

.data-value {
  font-size: 22rpx;
  color: #333;
  font-weight: 500;
}

.data-item.item-total {
  background-color: #fff2e8;
}

.data-item.item-total .data-value.total-price {
  color: #ff6b35;
  font-weight: bold;
  font-size: 22rpx;
}

/* 移除不再需要的报价记录费用明细样式 */

.fee-details {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.fee-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
  text-align: center;
}

.fee-list {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 16rpx;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.fee-item:last-child {
  border-bottom: none;
}

.fee-item.final-amount {
  margin-top: 8rpx;
  padding-top: 16rpx;
  font-weight: bold;
}

.fee-label {
  font-size: 26rpx;
  color: #666;
}

.fee-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.fee-value.coupon-amount {
  color: #52c41a;
  font-weight: 500;
}

.fee-item.final-amount .fee-label,
.fee-item.final-amount .fee-value {
  font-size: 28rpx;
  font-weight: bold;
}

.fee-item.final-amount .fee-value.final-price {
  color: #FF4D4F;
  font-size: 32rpx;
}
