<!--pages/settings/index.wxml-->
<view class="container">

  <!-- 设置列表 -->
  <view class="settings-list">
    <view class="settings-group">
      <view class="settings-item" bindtap="goToAccountSecurity">
        <view class="item-title">账号与安全</view>
        <view class="item-arrow"></view>
      </view>
      
      <view class="settings-item" bindtap="goToAddressManage">
        <view class="item-title">地址管理</view>
        <view class="item-arrow"></view>
      </view>
      
      <view class="settings-item" bindtap="goToNotificationSettings">
        <view class="item-title">消息通知</view>
        <view class="item-arrow"></view>
      </view>
    </view>
    
    <view class="settings-group">
      <view class="settings-item" bindtap="clearCache">
        <view class="item-title">清除缓存</view>
        <view class="item-value">{{cacheSize}}</view>
      </view>
      
      <view class="settings-item" bindtap="goToAboutUs">
        <view class="item-title">关于我们</view>
        <view class="item-arrow"></view>
      </view>
      
      <view class="settings-item" bindtap="goToFeedback">
        <view class="item-title">意见反馈</view>
        <view class="item-arrow"></view>
      </view>
    </view>
    
    <view class="settings-group">
      <view class="settings-item" bindtap="showPrivacyPolicy">
        <view class="item-title">隐私政策</view>
        <view class="item-arrow"></view>
      </view>
      
      <view class="settings-item" bindtap="showUserAgreement">
        <view class="item-title">用户服务协议</view>
        <view class="item-arrow"></view>
      </view>
    </view>
  </view>
  
  <!-- 退出登录按钮 -->
  <view class="logout-btn" bindtap="logout" wx:if="{{isLogin}}">退出登录</view>
  
  <!-- 版本信息 -->
  <view class="version-info">
    <text>当前版本 v1.0.0</text>
  </view>
</view>
