/**
 * 倒计时工具函数
 * 用于订单确认倒计时功能
 */

/**
 * 计算倒计时剩余时间
 * @param {string} startTime - 开始时间（ISO格式）
 * @param {number} durationMinutes - 倒计时时长（分钟）
 * @returns {object} 返回剩余时间信息
 */
function calculateCountdown(startTime, durationMinutes = 15) {
  if (!startTime) {
    return {
      isExpired: true,
      remainingSeconds: 0,
      displayText: '已过期'
    };
  }

  const startTimestamp = new Date(startTime).getTime();
  const currentTimestamp = Date.now();
  const durationMs = durationMinutes * 60 * 1000; // 转换为毫秒
  const endTimestamp = startTimestamp + durationMs;
  
  const remainingMs = endTimestamp - currentTimestamp;
  
  if (remainingMs <= 0) {
    return {
      isExpired: true,
      remainingSeconds: 0,
      displayText: '已过期'
    };
  }
  
  const remainingSeconds = Math.floor(remainingMs / 1000);
  const minutes = Math.floor(remainingSeconds / 60);
  const seconds = remainingSeconds % 60;
  
  return {
    isExpired: false,
    remainingSeconds: remainingSeconds,
    displayText: `（${minutes}分${seconds.toString().padStart(2, '0')}秒）`
  };
}

/**
 * 倒计时管理器类
 */
class CountdownManager {
  constructor() {
    this.timers = new Map(); // 存储所有活跃的倒计时
    this.callbacks = new Map(); // 存储回调函数
  }
  
  /**
   * 开始倒计时
   * @param {string} id - 倒计时唯一标识
   * @param {string} startTime - 开始时间
   * @param {function} callback - 更新回调函数
   * @param {number} durationMinutes - 倒计时时长（分钟）
   */
  start(id, startTime, callback, durationMinutes = 15) {
    // 清除已存在的倒计时
    this.stop(id);
    
    // 立即执行一次回调
    const initialResult = calculateCountdown(startTime, durationMinutes);
    callback(initialResult);
    
    // 如果已经过期，不需要启动定时器
    if (initialResult.isExpired) {
      return;
    }
    
    // 启动定时器
    const timer = setInterval(() => {
      const result = calculateCountdown(startTime, durationMinutes);
      callback(result);
      
      // 如果倒计时结束，清除定时器
      if (result.isExpired) {
        this.stop(id);
      }
    }, 1000);
    
    this.timers.set(id, timer);
    this.callbacks.set(id, callback);
  }
  
  /**
   * 停止倒计时
   * @param {string} id - 倒计时唯一标识
   */
  stop(id) {
    const timer = this.timers.get(id);
    if (timer) {
      clearInterval(timer);
      this.timers.delete(id);
      this.callbacks.delete(id);
    }
  }
  
  /**
   * 停止所有倒计时
   */
  stopAll() {
    this.timers.forEach((timer) => {
      clearInterval(timer);
    });
    this.timers.clear();
    this.callbacks.clear();
  }
  
  /**
   * 检查倒计时是否存在
   * @param {string} id - 倒计时唯一标识
   */
  exists(id) {
    return this.timers.has(id);
  }
}

// 创建全局倒计时管理器实例
const countdownManager = new CountdownManager();

module.exports = {
  calculateCountdown,
  CountdownManager,
  countdownManager
};
