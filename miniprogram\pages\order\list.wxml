<!--pages/order/list.wxml-->
<view class="container">
  <!-- 标签栏 -->
  <scroll-view class="tab-bar" scroll-x="true" enhanced show-scrollbar="{{false}}">
    <view
      class="tab-item {{currentTab === item.id ? 'active' : ''}}"
      wx:for="{{tabs}}"
      wx:key="id"
      bindtap="changeTab"
      data-tab="{{item.id}}"
    >
      {{item.name}}
    </view>
  </scroll-view>

  <!-- 订单列表 -->
  <view class="order-list">
    <view
      class="order-card"
      wx:for="{{orders}}"
      wx:key="id"
      bindtap="goToOrderDetail"
      data-orderid="{{item.orderId}}"
    >
      <view class="order-header">
        <view class="order-number" bindlongpress="copyOrderId" data-orderid="{{item.orderId}}">订单号: {{item.orderId}}</view>
        <view class="order-status {{item.status}}">{{item.statusText}}</view>
      </view>

      <view class="order-content">
        <view class="order-info">
          <view class="info-row">
            <text class="info-label">黄金类型:</text>
            <text class="info-value">{{item.goldTypeText}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">成色:</text>
            <text class="info-value">{{item.purity}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">预估重量:</text>
            <text class="info-value">{{item.estimatedWeight}}g</text>
          </view>

          <!-- 状态为4（已检测）时显示检测结果的最终价格 -->
          <view class="info-row" wx:if="{{item.status === 4 && item.quotationFinalAmount && item.quotationFinalAmount !== '0.00'}}">
            <text class="info-label">最终价格:</text>
            <text class="info-value final-price">¥{{item.quotationFinalAmount}}</text>
          </view>
          <!-- 状态为4但没有检测结果时，显示订单的最终价格 -->
          <view class="info-row" wx:elif="{{item.status === 4 && item.finalPrice && item.finalPrice !== '0.00'}}">
            <text class="info-label">最终价格:</text>
            <text class="info-value final-price">¥{{item.finalPrice}}</text>
          </view>
          <!-- 其他状态下显示最终价格（如果有） -->
          <view class="info-row" wx:elif="{{item.finalPrice && item.finalPrice !== '0.00' && item.goldType === 'jewelry' && item.status !== 4}}">
            <text class="info-label">最终价格:</text>
            <text class="info-value final-price">¥{{item.finalPrice}}</text>
          </view>
        </view>

        <view class="order-image" wx:if="{{item.hasImage}}">
          <image
            class="gold-image"
            src="{{item.firstImage}}"
            mode="aspectFill"
            lazy-load="{{true}}"
            binderror="onImageError"
            bindload="onImageLoad"
            data-orderid="{{item.orderId}}"
          ></image>
          <view class="image-count" wx:if="{{item.imageList.length > 1}}">
            {{item.imageList.length}}张
          </view>
        </view>
        <view class="order-image placeholder" wx:else>
          <view class="no-image">暂无图片</view>
        </view>
      </view>

      <view class="order-footer">
        <view class="order-actions">
          <!-- 已下单状态 -->
          <block wx:if="{{item.status === 1}}">
            <view class="order-action cancel" catchtap="cancelOrder" data-id="{{item.orderId}}" data-index="{{index}}">取消订单</view>
            <view class="order-action" catchtap="contactService" data-orderid="{{item.orderId}}">联系客服</view>
          </block>

          <!-- 待取件状态 -->
          <block wx:if="{{item.status === 2}}">
            <view class="order-action" catchtap="contactService" data-orderid="{{item.orderId}}">联系客服</view>
          </block>

          <!-- 待检测状态 -->
          <block wx:if="{{item.status === 3}}">
            <view class="order-action" catchtap="contactService" data-orderid="{{item.orderId}}">联系客服</view>
          </block>

          <!-- 已检测状态 -->
          <block wx:if="{{item.status === 4}}">
            <view class="order-action primary {{item.countdownExpired ? 'disabled' : ''}}" catchtap="confirmPrice" data-orderid="{{item.orderId}}" data-index="{{index}}">
              确认价格
              <text class="countdown-text" wx:if="{{item.countdownText}}">{{item.countdownText}}</text>
            </view>
            <view class="order-action" catchtap="contactService" data-orderid="{{item.orderId}}">联系客服</view>
          </block>

          <!-- 已确认状态 -->
          <block wx:if="{{item.status === 5}}">
            <view class="order-action" catchtap="contactService" data-orderid="{{item.orderId}}">联系客服</view>
          </block>

          <!-- 订单已完成状态 -->
          <block wx:if="{{item.status === 6}}">
            <view class="order-action" catchtap="contactService" data-orderid="{{item.orderId}}">联系客服</view>
          </block>

          <!-- 已取消状态 -->
          <block wx:if="{{item.status === 0}}">
            <view class="order-action" catchtap="contactService" data-orderid="{{item.orderId}}">联系客服</view>
          </block>
        </view>
      </view>

      <view class="order-time">提交时间: {{item.createTimeFormatted}}</view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container">
    <view class="loading" wx:if="{{loading}}">正在加载...</view>
    <view class="no-more" wx:if="{{!hasMore && orders.length > 0}}">没有更多订单了</view>
    <view class="empty" wx:if="{{!loading && orders.length === 0}}">
      <image class="empty-icon" src="/images/icons/empty-order.png"></image>
      <view class="empty-text">暂无订单</view>
      <navigator url="/pages/shop/shop" class="go-shopping">去购物</navigator>
    </view>
  </view>
</view>
