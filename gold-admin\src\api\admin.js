import request from '@/utils/request'

/**
 * 获取所有管理员列表
 */
export function getAdminList() {
  return request({
    url: '/api/admin/list',
    method: 'get'
  })
}

/**
 * 创建管理员
 * @param {object} adminData - 管理员数据
 */
export function createAdmin(adminData) {
  return request({
    url: '/api/admin/register',
    method: 'post',
    data: adminData,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 更新管理员状态
 * @param {string} adminId - 管理员ID
 * @param {number} status - 状态
 */
export function updateAdminStatus(adminId, status) {
  return request({
    url: `/api/admin/${adminId}/status`,
    method: 'put',
    params: { status }
  })
}
